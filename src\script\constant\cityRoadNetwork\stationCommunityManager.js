import { SHANDONG_CITY } from '@/script/constant/shandong';
const communityFormCols = [
    {
        prop: 'stationName',
        labelWidth: '95',
        label: '站点名称',
        element: 'el-input',
        attrs: {
            placeholder: '请输入',
            clearable: true
        },
        span: 4
    },
    {
        prop: 'cityId',
        label: '地市名称',
        element: 'el-select',
        attrs: {
            placeholder: '请选择',
            clearable: true,
            'popper-class': 'gdb-select-dropdown-dark gdb-popover-dark'
        },
        slot: {
            element: 'el-option',
            enums: SHANDONG_CITY({ type: 'metro' })
        },
        span: 4
    },
    {
        span: 16
    }
];

const communityTableCols = [
    {
        prop: 'trainStationName',
        label: '站点名称'
    },
    {
        prop: 'city',
        label: '归属地市'
    },
    {
        prop: 'district',
        label: '归属区县'
    },
    {
        prop: 'highSpeed',
        label: '是否高铁站'
    },
    {
        prop: 'intercity',
        label: '是否城际站'
    },
    {
        prop: 'moving',
        label: '是否动车站'
    },
    {
        prop: 'regular',
        label: '是否普铁站'
    },
    {
        prop: 'eciCnt',
        label: '小区ECI'
    },
    {
        prop: 'operation',
        label: '操作',
        width: 180
    }
];

const communityCheckTableCols = [
    {
        prop: 'indexNo',
        label: '序号',
        width: '50'
    },
    {
        prop: 'trainStationName',
        label: '站点名称'
    },
    {
        prop: 'lacCell',
        label: '小区ECI'
    },
    {
        prop: 'updateTime',
        label: '更新时间',
        'min-width': 180
    }
];

export { communityFormCols, communityTableCols, communityCheckTableCols };
