<template>
    <div class="check-content">
        <detailContent :row="row" action="check" />
        <Card title="可视化展示">
            <section class="section-wrapper">
                <div class="section-container">
                    <div class="gis-content">
                        <mtv-gis
                            class="home-map"
                            ref="mtvGis"
                            :totaloptions="gistotalOptions"
                            @onLoad="gisOnLoad"
                            :autoActive="false"
                        ></mtv-gis>
                    </div>
                </div>
            </section>
        </Card>
    </div>
</template>

<script>
import Card from '../../../components/Card/index.vue';
import detailContent from '../detailContent/index.vue';
import { gistotalOptions, getMayType, changeGisColor } from '@/script/constant/gis.js';

export default {
    name: 'check-content',
    components: {
        Card,
        detailContent
    },
    props: {
        row: {
            type: Object,
            default: () => ({})
        }
    },
    data() {
        return {
            gistotalOptions
        };
    },
    methods: {
        gisOnLoad() {
            this.dialogGIS = this.$refs.mtvGis.getEntity();
            // 设置底图
            if (getMayType() === 'default') {
                this.dialogGIS.tileLayerList['底图图层'].visible = false;
                //重写getUrl方法
                this.dialogGIS.tileLayerList['高德底图'].getUrl = (x, y, z) => {
                    return `${location.origin}/MapUrl/api/appmaptile?x=${x}&y=${y}&z=${z}`;
                };
            } else {
                this.dialogGIS.tileLayerList['高德底图'] &&
                    (this.dialogGIS.tileLayerList['高德底图'].visible = false);
            }
            changeGisColor(this.dialogGIS);
            this.getRoadInfo();
        },
        getRoadInfo() {
            $request(
                'post',
                'mtexapi/region-service/regionsubscribe/highWayDetails/getRoadDetail',
                {
                    roadSubIds: this.row.roadSubIds
                }
            ).then(({ data: res }) => {
                this.drawRoad(res);
            });
        },
        drawRoad(data) {
            if (!data || data.length === 0) {
                return;
            }
            //准备数据
            //高度间隔，处理重叠,视情况加大
            let htStep = 0.0001;
            let datas = data.map((item) => {
                return {
                    points: [
                        {
                            lat: Number(item.startLatLng.split(',')[0]),
                            lng: Number(item.startLatLng.split(',')[1]),
                            ht: htStep * 0
                        },
                        {
                            lat: Number(item.endLatLng.split(',')[0]),
                            lng: Number(item.endLatLng.split(',')[1]),
                            ht: htStep * 0
                        }
                    ],
                    color: 0x00ff00
                };
            });
            //创建图层
            let layer = new this.dialogGIS.layer();
            layer.visible = true;
            this.dialogGIS.gis.scene.add(layer.Group);

            //开启保持道路像素大小
            datas.autoScale = true;
            //道路宽度
            datas.width = 5;
            //创建道路模型
            let roadMesh = this.dialogGIS.meshList.road.create(datas);
            //模型添加进图层
            layer.add(roadMesh);
            //更新GIS
            this.dialogGIS.gis.needUpdate = true;
            const regionCoorsList = data.map((item) => {
                return {
                    lat: Number(item.startLatLng.split(',')[0]),
                    lng: Number(item.startLatLng.split(',')[1])
                };
            });
            this.dialogGIS.cameraControl.zoomByPoints(regionCoorsList, 1.2);
        }
    }
};
</script>

<style lang="less" scoped>
.check-content {
    min-height: 100%;
    height: max-content;
    display: flex;
    flex-direction: column;
    gap: 6px;

    .section-wrapper {
        display: flex;
        justify-content: center;
        align-items: center;

        .section-container {
            width: 60%;
            height: 100%;

            .gis-content {
                margin-top: 16px;
                position: relative;
                width: 100%;
                height: 0;
                padding: 0;
                padding-bottom: 56.25%;

                .home-map {
                    border-radius: 2px;
                    border: 1px solid rgba(64, 155, 255, 0.3);
                    position: absolute;
                    top: 0;
                    right: 0;
                    bottom: 0;
                    left: 0;
                }
            }
        }
    }
}
</style>
