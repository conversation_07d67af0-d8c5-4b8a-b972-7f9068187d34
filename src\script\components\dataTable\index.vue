<!-- 数据表格 -->
<template>
    <div class="data-table" ref="tables">
        <el-table
            ref="myTable"
            :size="size"
            :header-cell-style="theadColor"
            style="width: 100%"
            v-bind="$attrs"
            v-on="$listeners"
        >
            <!-- 自定义空内容 -->
            <template #empty>
                <slot name="empty"></slot>
            </template>
            <!-- 列 -->
            <el-table-column
                v-if="isSelectable"
                type="selection"
                reserve-selection
                width="36"
                :selectable="selectable"
            />
            <el-table-column v-for="item in columns" v-bind="item" :key="item.prop" align="center">
                <!-- 添加自定义表头插槽 -->
                <template #header>
                    <slot :name="`header-${item.prop}`">
                        {{ item.label }}
                    </slot>
                </template>

                <template #default="{ row, $index }">
                    <slot :name="item.prop" :row="row" :inx="$index">
                        {{ row[item.prop] }}
                    </slot>
                </template>
            </el-table-column>
        </el-table>
        <!-- 分页 -->
        <div class="wrap-pagination" :class="{ hideUpLine: isHideUpLine }">
            <slot name="pagination">
                <el-pagination
                    class="pagination"
                    :current-page="pagination.curPage"
                    :page-sizes="pagination.pageSizes"
                    :page-size="pagination.pageSize"
                    :total="total"
                    :layout="layout"
                    :pager-count="pagination.pagerCount"
                    background
                    popper-class="pagination-popper"
                    @size-change="handleSizeChange"
                    @current-change="handleCurrentChange"
                />
            </slot>
        </div>
    </div>
</template>

<script>
export default {
    name: 'data-table',
    inheritAttrs: false,
    props: {
        columns: {
            type: Array,
            default: () => [],
            required: true
        },
        size: {
            type: String,
            default: 'small'
        },
        theadStyle: {
            type: Object,
            default: () => ({})
        },
        updateTable: {
            type: Function,
            default: () => {},
            validator(value) {
                return typeof value === 'function';
            }
        },
        total: {
            type: Number,
            validator: (value) => {
                return value >= 0;
            }
        },
        layout: {
            type: String,
            default: 'total, prev, pager, next, sizes, jumper'
        },
        isSelectable: {
            type: Boolean,
            default: false
        },
        pagination: {
            type: Object,
            default: () => ({
                curPage: 1,
                pageSize: 15
            })
        },
        isHideUpLine: {
            type: Boolean,
            default: false
        },
        selectable: {
            type: Function
        }
    },
    data() {
        return {};
    },
    computed: {
        theadColor() {
            return {
                backgroundColor: '#F6F7FA',
                fontWeight: 'bold',
                color: '#4a4a4a',
                fontSize: '14px',
                ...this.theadStyle
            };
        }
    },
    created() {
        this.initPagination();
    },
    methods: {
        initPagination() {
            const { pageSize, pageSizes, pagerCount } = this.pagination;
            const mapPageSizes = {
                10: [10, 15, 25, 40],
                15: [15, 20, 30, 50]
            };
            if (!pageSizes || !pageSizes.length) {
                this.pagination.pageSizes = mapPageSizes[pageSize];
            }
            if (!pagerCount) {
                this.pagination.pagerCount = 7;
            }
        },
        handleSizeChange(pageSize) {
            Object.assign(this.pagination, {
                curPage: 1,
                pageSize
            });
            this.updateTable({
                curPage: 1,
                pageSize
            });
        },
        handleCurrentChange(curPage) {
            this.pagination.curPage = curPage;
            this.updateTable({
                curPage,
                pageSize: this.pagination.pageSize
            });
        },
        scrollToTop() {
            this.$nextTick(() => {
                const tableBody = this.$refs.myTable.$el.querySelector('.el-table__body-wrapper');
                if (tableBody) {
                    tableBody.scrollTop = 0; // 滚动到最上方
                }
            });
        },
        cleanSelect() {
            this.$refs.myTable.clearSelection();
        },
        toggleRowSelection(row, selected) {
            this.$refs.myTable.toggleRowSelection(row, selected);
        }
    }
};
</script>

<style lang="less" scoped>
.data-table {
    height: 100%;
    display: flex;
    flex-flow: column;
    .el-table {
        flex: 1;
        width: 100%;
        background-color: transparent;
        &::before {
            background: rgba(64, 155, 255, 0.4);
        }
        &::after {
            background: none;
        }
        /deep/ .el-table__header-wrapper .gutter {
            background-color: rgba(64, 155, 255, 0.4);
        }
        /deep/.el-table__header-wrapper {
            width: calc(100% - 12px) !important;
            tr {
                background-color: transparent;
            }
            th {
                color: rgba(255, 255, 255, 0.65) !important;
                background: transparent !important;
                border: none !important;
                background-color: #093e79 !important;
            }
        }
        /deep/.el-table__body-wrapper {
            height: calc(100% - 40px);
            overflow-y: auto;
            .el-table__row {
                &.success-row {
                    background-color: #0091ff;
                }
                .cell {
                    font-size: 14px;
                }
                &.custom-cell {
                    padding: 4px 0;
                    .cell {
                        padding-left: 4px;
                        padding-right: 4px;
                        line-height: 20px;
                    }
                }
            }
            .el-table__body {
                width: 100% !important;
            }
            // 自定义滚动条样式
            &::-webkit-scrollbar {
                width: 12px !important; // 设置滚动条宽度为10px
            }
            &::-webkit-scrollbar-thumb {
                border-radius: 10px;
                box-shadow: inset 0 0 5px rgba(0, 0, 0, 0.1);
                background: #0b66b3;
            }
            &::-webkit-scrollbar-track {
                /* 滚动条里面轨道 */
                box-shadow: inset 0 0 5px rgba(0, 0, 0, 0.1);
                border-radius: 10px;
                background: transparent;
            }
            &::-webkit-scrollbar-corner {
                background: rgba(0, 0, 0, 0);
            }
        }

        // 表行单元格底部横线颜色
        /deep/ td {
            color: rgba(255, 255, 255, 0.65);
            border: none;
            box-shadow: inset 0px -1px 0px 0px rgba(64, 155, 255, 0.3);
        }
        // 表行背景、hover颜色
        /deep/ tr {
            background-color: transparent;
            &:hover {
                background-color: #093e79;
            }
            &.current-row {
                background-color: #093e79;
            }
        }
        // 表行单元格(区分斑马纹)
        /deep/ .el-table__row td {
            background-color: transparent;
        }
        /deep/ .el-table__row--striped td {
            background: rgba(9, 62, 121, 0.4);
            box-shadow: inset 0px -1px 0px 0px rgba(64, 155, 255, 0.3);
        }
    }
    .wrap-pagination {
        margin-top: 13px;
        border-top: 1px solid #ddd;
        text-align: right;
        height: 40px;
    }
    .hideUpLine {
        margin-top: 5px;
        border-top: none;
    }
}

/deep/ .el-table th > .cell {
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

.el-pagination.pagination {
    display: flex;
    justify-content: flex-end;
    margin: 8px 0;
    padding-right: 0;
    /deep/ .el-pagination__sizes {
        margin-right: 0;
    }
    /deep/.el-pagination {
        &__total {
            color: #0095ff;
        }
        &__sizes {
            color: #0095ff;
        }
        &__jump {
            color: #0095ff;
        }
    }
    /deep/.el-pager li {
        color: #0095ff;
        background-color: transparent;
        box-shadow: inset 0px 0px 7px 0px rgba(18, 156, 237, 0.5);
        border-radius: 2px;
        border: 1px solid #0b66b3;
        &.active {
            background-color: transparent;
            box-shadow: inset 0px 0px 18px 0px rgba(18, 174, 237, 0.8);
            border: 1px solid #009eff;
            color: #ffffff;
        }
        &:hover {
            color: #ffffff;
        }
    }
    /deep/.el-input__inner {
        color: #0095ff;
        background: transparent;
        box-shadow: inset 0px 0px 7px 0px rgba(18, 156, 237, 0.5);
        border-radius: 2px;
        border: 1px solid #0b66b3;
    }
    /deep/ .el-input__suffix {
        .el-select__caret {
            color: #0095ff;
        }
    }
    /deep/button {
        color: #0095ff;
        background-color: transparent;
        box-shadow: inset 0px 0px 7px 0px rgba(18, 156, 237, 0.5);
        border-radius: 2px;
        border: 1px solid #0b66b3;
        &:disabled {
            color: #0095ff66;
        }
        &.btn-next {
            &:hover {
                color: #fff;
            }
        }
    }
}
</style>
<style lang="less">
.pagination-popper {
    background: #002a5c !important;
    border: 1px solid rgba(64, 155, 255, 0.3) !important;
    &[x-placement^='top'] .popper__arrow {
        border-top-color: rgba(64, 155, 255, 0.3) !important;
        &::after {
            border-top-color: #002a5c !important;
        }
    }
    &[x-placement^='bottom'] .popper__arrow {
        border-bottom-color: rgba(64, 155, 255, 0.3) !important;
        &::after {
            border-bottom-color: #002a5c !important;
        }
    }
    &[x-placement^='left'] .popper__arrow {
        border-left-color: rgba(64, 155, 255, 0.3) !important;
        &::after {
            border-left-color: #002a5c !important;
        }
    }
    &[x-placement^='right'] .popper__arrow {
        border-right-color: rgba(64, 155, 255, 0.3) !important;
        &::after {
            border-right-color: #002a5c !important;
        }
    }
    // 暗色时滚动条调成透明
    .el-scrollbar__wrap {
        &::-webkit-scrollbar {
            width: 6px;
            height: 6px;
        }
        &::-webkit-scrollbar-track {
            box-shadow: inset 0 0 5px transparent;
            border-radius: 10px;
            background: transparent;
        }
    }
    .el-select-dropdown__item {
        color: #fff;
        &:last-child {
            margin-bottom: 10px;
        }
        &.selected:not(.is-disabled) {
            color: #0095ff;
        }
        &.hover {
            background-color: #093e79;
        }
        &:hover {
            background-color: #093e79;
        }
    }
}
</style>
