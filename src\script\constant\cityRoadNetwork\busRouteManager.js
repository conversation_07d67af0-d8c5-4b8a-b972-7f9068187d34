import { SHANDONG_CITY } from '@/script/constant/shandong';
const routeFormCols = [
    {
        prop: 'line',
        label: '线路名称',
        element: 'el-input',
        attrs: {
            placeholder: '请输入',
            clearable: true
        },
        span: 4
    },
    {
        prop: 'cityId',
        label: '归属城市',
        element: 'el-select',
        attrs: {
            placeholder: '请选择',
            clearable: true,
            'popper-class': 'gdb-select-dropdown-dark gdb-popover-dark'
        },
        slot: {
            element: 'el-option',
            enums: SHANDONG_CITY()
        },
        span: 4
    },
    {
        span: 16
    }
];

const routeTableCols = [
    {
        prop: 'line',
        label: '公交线路名称'
    },
    {
        prop: 'lineName',
        label: '线路别名'
    },
    {
        prop: 'cityName',
        label: '归属城市'
    },
    {
        prop: 'geometry',
        label: '站点序列'
    },
    {
        prop: 'status',
        label: '运营状态'
    },
    {
        prop: 'updateTime',
        label: '最后更新时间',
        'min-width': 180
    },
    {
        prop: 'updateUser',
        label: '最后更新用户'
    },
    {
        prop: 'operation',
        label: '操作',
        width: 180
    }
];

export { routeFormCols, routeTableCols };
