<template>
    <div class="left-nav">
        <div
            class="nav-item"
            v-for="(item, index) in data"
            :key="index"
            :class="{ active: currentNav === item.typeName }"
            @click="handleNavClick(item)"
        >
            <i :class="[item.icon, { 'icon-active': currentNav === item.typeName }]"></i>
            <span>{{ item.typeName }}</span>
        </div>
    </div>
</template>

<script>
export default {
    name: 'LeftNav',
    props: {
        currentNav: {
            type: String,
            default: '城市路网'
        },
        data: {
            type: Array,
            default: []
        }
    },
    methods: {
        handleNavClick(item) {
            this.$emit('update:currentNav', item.typeName);
            this.$emit('nav-change', item);
        }
    }
};
</script>

<style lang="less" scoped>
.left-nav {
    width: 11.11rem;
    height: 100%;
    .nav-item {
        border-radius: 0.5rem;
        padding: 0.67rem 2.22rem;
        cursor: pointer;
        display: flex;
        align-items: center;
        color: #92b9e5;
        font-size: 1.11rem;
        transition: all 0.3s;
        margin-bottom: 0.56rem;

        i {
            color: #2f98ff;
            margin-right: 0.56rem;
            font-size: 1.11rem;
        }

        &:hover,
        &.active {
            color: #fff;
            background: linear-gradient(180deg, #1c7691 0%, #093252 48%, #1659a8 100%);
            border-radius: 0.39rem;
            border: 1px solid #90e5ff;
        }
        .icon-active {
            color: #fff;
        }
        span {
            font-weight: 500;
            font-size: 1.11rem;
            font-family:
                Source Han Sans CN,
                Source Han Sans CN;
        }
    }
}
</style>
