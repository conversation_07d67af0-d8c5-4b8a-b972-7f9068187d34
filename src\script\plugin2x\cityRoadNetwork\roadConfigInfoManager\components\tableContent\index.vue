<template>
    <commonTableContent
        ref="roadConfigInfoListTable"
        :fields="fields"
        :initialForm="initialForm"
        addButtonText="新增道路信息"
        :columns="columns"
        :data="tableData"
        :dataTotal="total"
        @get-table-data="handleGetTableData"
        @action="handleAction"
        @delete="handleDelete"
    >
    </commonTableContent>
</template>

<script>
import commonTableContent from '../../../components/commonTableContent/index.vue';
import {
    infoFormCols,
    infoTableCols
} from '@/script/constant/cityRoadNetwork/roadConfigInfoManager.js';
import { getLabelByValue } from '@/script/utils/method';
import { SHANDONG_CITY } from '@/script/constant/shandong';

export default {
    name: 'table-content',
    components: {
        commonTableContent
    },
    data() {
        return {
            initialForm: {
                cityId: '',
                roadName: ''
            },
            total: 0,
            tableData: []
        };
    },
    computed: {
        fields() {
            return infoFormCols;
        },
        columns() {
            return infoTableCols;
        }
    },
    methods: {
        async handleGetTableData(params, pagination) {
            const payload = {
                cityId: Number(params.cityId) || undefined,
                cityName: getLabelByValue(SHANDONG_CITY(), params.cityId),
                roadName: params.roadName,
                pageNum: pagination.curPage,
                pageSize: pagination.pageSize
            };

            let { data } = await $request(
                'post',
                'mtexapi/region-service/cityAndRoadManagement/page',
                payload
            );
            this.tableData = data.list;
            this.total = data.pageTotal;
        },
        handleAction(action, row = {}) {
            // 将action转发到父组件
            this.$emit('action', action, row);
        },
        handleDelete(row) {
            this.$confirm('确定要删除吗？', '提示', {
                confirmButtonText: '确定',
                cancelButtonText: '取消',
                type: 'warning'
            }).then(() => {
                $request('post', 'mtexapi/region-service/cityAndRoadManagement/delete', {
                    roadId: row.roadId
                })
                    .then(({ serviceFlag, returnMsg }) => {
                        if (serviceFlag === 'TRUE') {
                            this.refreshTable();
                            this.$message({
                                type: 'success',
                                message: '删除成功'
                            });
                        } else {
                            this.$message({
                                type: 'error',
                                message: returnMsg
                            });
                        }
                    })
                    .catch(() => {});
            });
        },
        refreshTable() {
            this.$refs.roadConfigInfoListTable.search();
        }
    },
    mounted() {
        this.refreshTable();
    }
};
</script>
