<template>
    <commonTableContent
        ref="personalMetroDataListTable"
        :fields="fields"
        :initialForm="initialForm"
        addButtonText="新增出行统计"
        :columns="columns"
        :data="tableData"
        :dataTotal="total"
        @get-table-data="handleGetTableData"
        @action="handleAction"
        @delete="handleDelete"
    >
    </commonTableContent>
</template>

<script>
import commonTableContent from '../../../components/commonTableContent/index.vue';
import {
    infoFormCols,
    infoTableCols
} from '@/script/constant/cityRoadNetwork/personalMetroDataManager.js';

export default {
    name: 'table-content',
    components: {
        commonTableContent
    },
    data() {
        return {
            initialForm: {
                statisticType: '3',
                cityId: '',
                stationName: '',
                timeRange: []
            },
            total: 0,
            tableData: [],
            fields: infoFormCols({
                fun: this.handleTabChange,
                nowTab: '3'
            }),
            nowTab: '3'
        };
    },
    computed: {
        columns() {
            return infoTableCols;
        }
    },
    created() {
        this.timeGranularityChange();
    },
    methods: {
        async timeGranularityChange() {
            const tab = this.nowTab;
            // 根据tab的值，修改fields(主要是时间的粒度变化)
            this.fields = infoFormCols({
                fun: this.handleTabChange,
                nowTab: tab
            });
        },
        handleTabChange(tab) {
            this.nowTab = tab;
            this.timeGranularityChange();
        },
        async handleGetTableData(params, pagination) {
            const payload = {
                startTime: Array.isArray(params.timeRange) ? params.timeRange[0] : undefined,
                endTime: Array.isArray(params.timeRange) ? params.timeRange[1] : undefined,
                timeType: +params.statisticType,
                cityId: params.cityId ? +params.cityId : undefined,
                subStationName: params.stationName,
                pageNo: pagination.curPage,
                pageSize: pagination.pageSize
            };

            let { data } = await $request(
                'post',
                'mtexapi/region-service/personal/sub/info/query',
                payload
            );
            this.tableData = data.list.map((item) => {
                return {
                    ...item,
                    statisticType: params.statisticType
                };
            });
            this.total = data.total;
        },
        handleAction(action, row = {}) {
            // 将action转发到父组件
            this.$emit('action', action, { ...row });
        },
        handleDelete(row) {
            // 删除逻辑
            this.$confirm('确定要删除吗？', '提示', {
                confirmButtonText: '确定',
                cancelButtonText: '取消',
                type: 'warning'
            })
                .then(() => {
                    $request('post', 'mtexapi/region-service/personal/sub/info/delete', {
                        time: row.time,
                        cityId: row.cityId,
                        subwayStationId: row.subwayStationId
                    })
                        .then(({ serviceFlag, returnMsg }) => {
                            if (serviceFlag === 'TRUE') {
                                this.refreshTable();
                                this.$message({
                                    type: 'success',
                                    message: '删除成功'
                                });
                            } else {
                                this.$message({
                                    type: 'error',
                                    message: returnMsg
                                });
                            }
                        })
                        .catch(() => {});
                })
                .catch(() => {
                    // 用户取消删除
                });
        },
        refreshTable() {
            this.timeGranularityChange();
            this.$refs.personalMetroDataListTable.search();
        }
    },
    mounted() {
        this.refreshTable();
    }
};
</script>
