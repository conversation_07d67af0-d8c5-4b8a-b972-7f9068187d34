<!--
  SearchBar组件 - 基于Form组件的搜索栏封装
  功能：
  1. 提供统一的搜索表单界面
  2. 处理搜索相关的事件
  3. 支持自定义搜索项配置
-->
<template>
    <div class="search-bar">
        <Form
            ref="searchForm"
            :form.sync="formData"
            :formConfig="formItems"
            :labelWidth="labelWidth"
            :rules="rules"
            :inline="true"
            @change="handleChange"
            @remote-method="handleRemoteMethod"
            @initDefaultSearch="handleSearch"
            v-if="isConfigLoaded"
        >
        </Form>

        <div class="search-buttons">
            <slot name="buttons">
                <el-button type="primary" size="small" @click="handleSearch">搜索</el-button>
                <!-- <el-button @click="handleReset">重置</el-button> -->
            </slot>
        </div>
    </div>
</template>

<script>
import Form from '../Form/index.vue';

export default {
    name: 'SearchBar',
    components: {
        Form
    },
    props: {
        // 搜索表单配置
        formConfig: {
            type: Array,
            default: () => []
        },
        // 标签宽度
        labelWidth: {
            type: String,
            default: 'auto'
        },
        // 验证规则
        rules: {
            type: Object,
            default: () => ({})
        }
    },
    data() {
        return {
            formItems: [],
            formData: {},
            isConfigLoaded: false
        };
    },
    created() {
        this.initFormItems();
    },
    methods: {
        isCityOrDistinct(formItemObj, key) {
            return formItemObj[key] == '地市' || formItemObj[key] == '区县';
        },
        // 获取表单下拉数据
        async getOptionsList(formItemObj) {
            let options = [];
            let { componentConfig } = formItemObj;
            //根据关联字段传参  例如：地铁站点->关联字段为区县id-districtId
            let defaultParamsVal = this.formConfig.filter(
                (e) => e.labelParam == formItemObj.linkKey
            );
            // 修改后
            if (defaultParamsVal.length) {
                defaultParamsVal = defaultParamsVal[0].defaultValue;
            }
            //地市跟区县-特殊处理
            if (componentConfig && componentConfig.url) {
                try {
                    if (this.isCityOrDistinct(formItemObj, 'labelName')) {
                        let { data } = await $request(
                            'post',
                            `mtexapi/region-service${componentConfig.url}`,
                            {
                                districtCode:
                                    formItemObj.labelName == '地市' ? 370000 : defaultParamsVal
                            }
                        );

                        options = data.map((item) => {
                            return {
                                key: item.value,
                                label: item.key
                            };
                        });
                    } else {
                        let { data } = await $request(
                            'post',
                            `mtexapi/region-service${componentConfig.url}`,
                            {
                                districtId: defaultParamsVal
                            }
                        );
                        //目前暂时只处理了地铁站点的下拉列表
                        options = data.map((item) => {
                            return {
                                key: item.stationId,
                                label: item.stationName
                            };
                        });
                    }
                    return options; // 添加返回语句
                } catch (error) {
                    console.error('获取选项数据失败:', error);
                    return []; // 错误时返回空数组
                }
            } else if (componentConfig && componentConfig.options) {
                options = componentConfig.options;
            }
            return options; // 如果没有 url，返回空数组
        },
        // 初始化表单项
        async initFormItems() {
            const formItems = [];
            try {
                for (const item of this.formConfig) {
                    // 等待获取选项数据
                    const options = await this.getOptionsList(item);

                    // 处理默认值
                    let defaultVal = item.defaultValue;
                    if (options.length > 0) {
                        defaultVal = options[0].key;
                    }
                    // 构建表单项
                    const formItem = {
                        key: item.labelParam,
                        label: item.labelName,
                        labelWidth: item.labelWidth,
                        componentId: item.componentId,
                        defaultVal: defaultVal,
                        linkKey: item.linkKey,
                        span: item.span,
                        componentConfig: item.componentConfig,
                        options: options || [],
                        ...(item.componentConfig || {}),
                        _originalConfig: item
                    };
                    // 添加到数组
                    formItems.push(formItem);
                }
                this.formItems = formItems;
                this.isConfigLoaded = true; // 数据加载完成后设置标志
            } catch (error) {
                this.$message.error('加载表单配置失败');
            }
        },
        // 处理搜索
        handleSearch() {
            const formInstance = this.$refs.searchForm;
            formInstance
                .validate()
                .then(() => {
                    const formData = formInstance.getFormData();
                    this.$emit('search', formData);
                })
                .catch((err) => {
                    console.error('表单验证失败:', err);
                });
        },
        // 处理重置
        handleReset() {
            const formInstance = this.$refs.searchForm;
            formInstance.reset();
            this.$emit('reset');
        },
        // 处理表单变更
        handleChange(data) {
            // 处理联动逻辑
            this.handleLinkage(data);
            this.$emit('change', data);
        },
        // 处理表单项联动
        async handleLinkage(data) {
            const { key, val } = data;
            if (!key) {
                return;
            }

            // 查找依赖项
            const dependentItems = this.formItems.filter((item) => item.linkKey === key);

            // 更新所有依赖项
            const updatePromises = dependentItems.map((item) =>
                this.updateDependentItemOptions(item, val)
            );

            try {
                await Promise.all(updatePromises);
            } catch (error) {
                console.error('更新联动选项失败:', error);
            }
        },
        // 添加新的方法来处理选项更新
        //地市-->区县
        //地市-->地铁站
        async updateDependentItemOptions(item, val) {
            let newOptions = [];
            if (!item.componentConfig.url) {
                return;
            }
            try {
                //地市与区县
                if (this.isCityOrDistinct(item, 'label')) {
                    let { data: options } = await $request(
                        'post',
                        `mtexapi/region-service${item.componentConfig.url}`,
                        {
                            districtCode: val
                        }
                    );

                    newOptions = options.map((opt) => ({
                        key: opt.value,
                        label: opt.key
                    }));
                } else {
                    //地铁站点的下拉列表

                    let { data: options } = await $request(
                        'post',
                        `mtexapi/region-service${item.componentConfig.url}`,
                        {
                            districtId: val
                        }
                    );
                    newOptions = options.map((opt) => ({
                        key: opt.stationId,
                        label: opt.stationName
                    }));
                }

                this.updateFormItemOptions(item.key, newOptions);
            } catch (error) {
                this.$message.error(`更新${item.label}选项失败`);
            }
        },

        // 添加新的方法来更新表单项选项
        updateFormItemOptions(itemKey, newOptions) {
            const index = this.formItems.findIndex((i) => i.key === itemKey);
            if (index === -1) {
                return;
            }

            // 更新选项
            this.$set(this.formItems[index], 'options', newOptions);

            // 更新默认值
            const defaultValue = newOptions.length > 0 ? newOptions[0].key : '';
            this.$set(this.formData, itemKey, defaultValue);
        },

        // 处理远程搜索
        handleRemoteMethod(componentInfo, value) {
            this.$emit('remoteSearch', { componentInfo, value });
        }
    }
};
</script>

<style lang="less" scoped>
.search-bar {
    background: #002a59;
    border-radius: 4px;
    display: flex;
    align-items: center;

    .search-buttons {
    }
}
</style>
