<template>
    <div class="content-container">
        <mtv-gis :ref="gisId" :totaloptions="gisOptions" @onLoad="gisOnLoad"></mtv-gis>
    </div>
</template>

<script>
import { gistotalOptions, getMayType, changeGisColor } from '@/script/constant/gis.js';
export default {
    props: {
        type: {
            type: String,
            default: 'point'
        },
        resultData: {
            type: Array,
            default: () => []
        }
    },
    watch: {
        resultData: {
            handler(newVal) {
                this.clearGisData();
                if (
                    !newVal.some((item) => {
                        return item.longitude && item.latitude;
                    })
                ) {
                    return;
                }
                this.listData = newVal.map((item) => {
                    return {
                        lng: item.longitude,
                        lat: item.latitude,
                        width: 29,
                        ht: 0.2,
                        rawData: { ...item }
                    };
                });

                this.$nextTick(() => {
                    const fun = () => {
                        this.clearGisData();
                        if (this.type === 'point') {
                            this.drawLocationImg(this.listData);
                        } else if (this.type === 'line') {
                            this.drawRoad(this.listData);
                        }
                    };
                    if (!this.gisOnLoaded) {
                        setTimeout(fun, 100);
                        return;
                    }
                    fun();
                });
            },
            deep: true
        }
    },
    data() {
        return {
            dialogGIS: null,
            gisId: 'gisId',
            gisOptions: gistotalOptions,
            dateActive: 0,
            dates: ['02.18', '02.19', '02.20', '02.21', '02.22', '02.23', '02.24'],
            times: ['0', '1', '2', '3', '4', '5', '6'],
            timeActive: 0,
            listData: [],
            // gis
            imgLayer: null,
            divLayer: [],
            activeData: [],
            gisOnLoaded: false,
            isListenerAdded: false
        };
    },
    methods: {
        gisOnLoad() {
            this.dialogGIS = this.$refs[this.gisId].getEntity();
            // 设置底图
            if (getMayType() === 'default') {
                this.dialogGIS.tileLayerList['底图图层'].visible = false;
                //重写getUrl方法
                this.dialogGIS.tileLayerList['高德底图'].getUrl = (x, y, z) => {
                    return `${location.origin}/MapUrl/api/appmaptile?x=${x}&y=${y}&z=${z}`;
                };
            } else {
                this.dialogGIS.tileLayerList['高德底图'] &&
                    (this.dialogGIS.tileLayerList['高德底图'].visible = false);
            }
            changeGisColor(this.dialogGIS);
            this.gisOnLoaded = true;
        },
        clearGisData() {
            this.dialogGIS && this.dialogGIS.layerList.divLayer.removeAll();
            if (this.imgLayer) {
                this.imgLayer.removeAll();
            }
        },
        drawRoad(data) {
            if (!data || data.length === 0) {
                return;
            }
            //准备数据
            //高度间隔，处理重叠,视情况加大
            let htStep = 0.0001;
            let datas = data.map((item) => {
                return {
                    points: [
                        {
                            lat: Number(item.startLatLng.split(',')[0]),
                            lng: Number(item.startLatLng.split(',')[1]),
                            ht: htStep * 0
                        },
                        {
                            lat: Number(item.endLatLng.split(',')[0]),
                            lng: Number(item.endLatLng.split(',')[1]),
                            ht: htStep * 0
                        }
                    ],
                    color: 0x00ff00
                };
            });
            //创建图层
            let layer = new this.dialogGIS.layer();
            layer.visible = true;
            this.dialogGIS.gis.scene.add(layer.Group);

            //开启保持道路像素大小
            datas.autoScale = true;
            //道路宽度
            datas.width = 5;
            //创建道路模型
            let roadMesh = this.dialogGIS.meshList.road.create(datas);
            //模型添加进图层
            layer.add(roadMesh);
            //更新GIS
            this.dialogGIS.gis.needUpdate = true;
            const regionCoorsList = data.map((item) => {
                return {
                    lat: Number(item.startLatLng.split(',')[0]),
                    lng: Number(item.startLatLng.split(',')[1])
                };
            });
            this.dialogGIS.cameraControl.zoomByPoints(regionCoorsList, 1.2);
        },
        drawLocationImg(list) {
            let data = list;
            const GIS = this.$refs[this.gisId].getEntity();
            this.imgLayer = new GIS.layer();
            this.imgLayer.name = 'imgTest';
            GIS.gis.scene.add(this.imgLayer.Group);
            this.imgLayer.visible = true;
            //传入图片url获取材质
            let material = GIS.meshList.img.getMaterial({
                url: require('../../../img/common/mark-green.png'),
                opacity: 1
            });
            //图片1
            data.autoScale = true;
            //生成模型
            let imgMesh = GIS.meshList.img.create(data, material);
            //模型添加进图层
            this.imgLayer.add(imgMesh);
            //更新GIS
            GIS.gis.needUpdate = true;
            if (data.length === 1) {
                GIS.cameraControl.move(data[0]);
                GIS.cameraControl.zoom = 17;
                return;
            }
            const zoomList = data.map((item) => {
                return {
                    lat: item.lat,
                    lng: item.lng
                };
            });
            GIS.cameraControl.zoomByPoints(zoomList, 1.4);
            const clickHandler = (event) => {
                let obj = GIS.math.rayCaster(2, { e: event, array: [this.imgLayer] });
                if (obj !== undefined) {
                    // 获取选中的物体的数据index
                    let index = GIS.meshList.img.getIndex(obj);
                    if (this.activeData.includes(index)) {
                        this.divLayer[index] && this.divLayer[index].remove();
                        this.activeData = this.activeData.filter((item) => item !== index);
                        return;
                    }
                    this.activeData.push(index);
                    this.drawingText([this.listData[index]], index);
                }
            };
            // 使用一个标志位来跟踪
            if (!this.isListenerAdded) {
                GIS.dom[0].addEventListener('click', clickHandler);
                this.isListenerAdded = true;
            }
        },
        drawingText(data, index) {
            if (!data.length || data.some((item) => !item)) {
                return;
            }
            const GIS = this.$refs[this.gisId].getEntity();

            //准备数据
            let datas = data.map((item) => {
                return {
                    dom: `<div
                            style="
                                border-radius: 2px;
                                background: #002858;
                                border: 1px solid #77AAEA;
                                color: #c3dff6;
                                padding: 8px 16px;
                                width: max-content;
                                font-size: 12px;
                                transform: translate(-50%, calc(-100% - 24px));
                            "
                        >
                            当前位置：${item.rawData.longitude},${item.rawData.latitude}<br />
                            ${this.objectToHtml(item.rawData)}
                        </div>`,
                    point: { lng: item.lng, lat: item.lat }
                };
            });
            //调用相关接口
            datas.forEach((item) => {
                this.divLayer[index] = GIS.layerList.divLayer.addDiv(item);
            });

            GIS.gis.needUpdate = true;
        },
        objectToHtml(obj, excludeFields = ['latitude', 'longitude']) {
            return (
                Object.entries(obj)
                    .filter(([key]) => !excludeFields.includes(key))
                    .map(([key, value]) => `${key}：${value}`)
                    .join('<br />') + '<br />'
            );
        },
        chooseDate(date) {
            this.dateActive = date;
        },
        chooseTime(time) {
            this.timeActive = time;
        }
    }
};
</script>

<style lang="less" scoped>
.content-container {
    width: 100%;
    height: 55vh;
    .mtv-gis {
        width: 100%;
        height: 100%;
        margin: 1.11rem 0;
        border-radius: 0.22rem;
        overflow: hidden;
    }
}
</style>
