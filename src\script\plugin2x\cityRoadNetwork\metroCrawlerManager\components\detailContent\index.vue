<template>
    <div class="content">
        <Card title="爬取任务信息">
            <div class="form-wrapper">
                <BaseForm
                    ref="baseForm"
                    :form-data="formData"
                    :form-config="formConfig"
                    :rules="rules"
                    :action="action"
                    :visible-fields="nowFields"
                    @submit="handleSubmit"
                />
            </div>
        </Card>
    </div>
</template>

<script>
import Card from '../../../components/Card/index.vue';
import BaseForm from '../../../components/BaseForm/index.vue';
import { getLabelByValue } from '@/script/utils/method.js';
import { SHANDONG_CITY } from '@/script/constant/shandong';
import { SCRAPE_OBJECT } from '@/script/constant/cityRoadNetwork/metroCrawlerManager.js';

export default {
    name: 'detail-content',
    components: {
        Card,
        BaseForm
    },
    props: {
        row: {
            type: Object,
            default: () => ({})
        },
        action: {
            type: String,
            default: 'check'
        }
    },
    data() {
        return {
            formData: {
                cityCode: '',
                scrapeObject: '',
                taskName: '',
                scrapeCount: 0,
                taskStatus: 3
            },
            nowFields: {
                add: ['taskName', 'scrapeObject', 'cityCode'],
                edit: ['taskName'],
                check: []
            },
            formConfig: [
                {
                    prop: 'taskName',
                    label: '任务名称：',
                    type: 'input',
                    placeholder: '请输入',
                    attrs: {
                        clearable: true
                    }
                },
                {
                    prop: 'scrapeObject',
                    label: '对象选择：',
                    type: 'el-select',
                    placeholder: '请选择',
                    options: SCRAPE_OBJECT,
                    attrs: {
                        clearable: true,
                        'popper-class': 'gdb-select-dropdown-dark gdb-popover-dark'
                    },
                    formatter: (value) => getLabelByValue(SCRAPE_OBJECT, value)
                },
                {
                    prop: 'cityCode',
                    label: '城市：',
                    type: 'el-select',
                    placeholder: '请选择',
                    options: SHANDONG_CITY(),
                    attrs: {
                        clearable: true,
                        'popper-class': 'gdb-select-dropdown-dark gdb-popover-dark'
                    },
                    formatter: (value) => getLabelByValue(SHANDONG_CITY(), value)
                }
            ],
            rules: {
                taskName: [{ required: true, message: '请输入任务名称', trigger: 'blur' }],
                cityCode: [{ required: true, message: '请选择城市', trigger: 'change' }],
                scrapeObject: [{ required: true, message: '请选择对象', trigger: 'change' }]
            }
        };
    },
    computed: {
        modeText() {
            const map = new Map([
                ['add', '新增'],
                ['edit', '修改'],
                ['check', '查看']
            ]);
            return map.get(this.action);
        }
    },
    methods: {
        handleSubmit(form, modeText) {
            const actionMap = (action) => {
                const map = {
                    add: {
                        path: 'add',
                        payload: {
                            cityCode: form.cityCode,
                            scrapeObject: form.scrapeObject,
                            taskName: form.taskName,
                            scrapeCount: 0,
                            taskStatus: 3 // 待启动
                        }
                    },
                    edit: {
                        path: 'update',
                        payload: {
                            id: this.row.id,
                            taskName: form.taskName
                        }
                    }
                };
                if (map[action]) {
                    return map[action];
                }
                return {};
            };
            $request(
                'post',
                `mtexapi/region-service/card/task/${actionMap(this.action).path}`,
                actionMap(this.action).payload
            ).then(({ serviceFlag, returnMsg }) => {
                if (serviceFlag === 'TRUE') {
                    this.$emit('goBack');
                    this.$message({
                        type: 'success',
                        message: `${modeText}成功`
                    });
                    return;
                }
                this.$message({
                    type: 'error',
                    message: returnMsg
                });
            });
        }
    },
    created() {
        if (['edit', 'check'].includes(this.action) && this.row) {
            this.formData = {
                ...this.formData,
                ...this.row
            };
        }
    }
};
</script>

<style lang="less" scoped>
.content {
    min-height: 100%;
    height: max-content;
    display: flex;
    flex-direction: column;

    .form-wrapper {
        display: flex;
        justify-content: center;
        align-items: center;
    }
}
</style>
