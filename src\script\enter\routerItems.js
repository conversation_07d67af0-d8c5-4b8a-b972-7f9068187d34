// routerItem: {
//     path: 'homePage/index', //首页 ->首页
//     multiple: false
// }
export default [
    { path: 'displayBoard/index' }, //首页
    { path: 'cityRoadNetwork/metroPOIManager/index' }, // 城市路网/地铁POI管理
    { path: 'cityRoadNetwork/busStopManager/index' }, // 城市路网/公交站点管理
    { path: 'cityRoadNetwork/busRouteManager/index' }, // 城市路网/公交线路管理
    { path: 'cityRoadNetwork/highwayInfoManager/index' }, // 城市路网/高速道路信息管理
    { path: 'cityRoadNetwork/highwayCommunityRelationManager/index' }, // 城市路网/高速道路小区关系指标管理
    { path: 'cityRoadNetwork/roadConfigInfoManager/index' }, // 城市路网/道路配置信息管理
    { path: 'cityRoadNetwork/stationInfoManager/index' }, // 城市路网/高铁站信息人工维护管理
    { path: 'cityRoadNetwork/stationCommunityManager/index' }, // 城市路网/高铁站区域与小区对应关系管理
    { path: 'cityRoadNetwork/cityGridManager/index' }, // 城市路网/城市网格管理
    { path: 'cityRoadNetwork/personalMetroDataManager/index' }, // 城市路网/个人地铁出行汇总数据管理
    { path: 'cityRoadNetwork/23GMetroCommunityManager/index' }, // 城市路网/23G地铁小区信息管理
    { path: 'cityRoadNetwork/4GMetroCommunityManager/index' }, // 城市路网/4G地铁小区信息管理
    { path: 'cityRoadNetwork/metroCrawlerManager/index' }, // 城市路网/地铁爬取任务管理
    { path: 'cityRoadNetwork/roadConditionView/index' }, // 城市路网/城市道路路况展示
    { path: 'cityRoadNetwork/cellSiteRelationView/index' } // 城市路网/基站小区与线路站点关系呈现
];
