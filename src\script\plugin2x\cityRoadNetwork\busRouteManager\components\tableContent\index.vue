<template>
    <commonTableContent
        ref="busRouteListTable"
        :fields="fields"
        :initialForm="initialForm"
        addButtonText=""
        :columns="columns"
        :data="tableData"
        :dataTotal="total"
        @get-table-data="handleGetTableData"
        @action="handleAction"
        @delete="handleDelete"
    >
    </commonTableContent>
</template>

<script>
import commonTableContent from '../../../components/commonTableContent/index.vue';
import {
    routeFormCols,
    routeTableCols
} from '@/script/constant/cityRoadNetwork/busRouteManager.js';

export default {
    name: 'table-content',
    components: {
        commonTableContent
    },
    data() {
        return {
            initialForm: {
                line: undefined,
                lineName: undefined,
                cityName: undefined,
                cityId: ''
            },
            total: 0,
            tableData: []
        };
    },
    computed: {
        fields() {
            return routeFormCols;
        },
        columns() {
            return routeTableCols;
        }
    },
    methods: {
        async handleGetTableData(params, pagination) {
            const payload = {
                line: params.line,
                lineName: params.lineName,
                cityId: params.cityId,
                pageNo: pagination.curPage,
                pageSize: pagination.pageSize
            };

            let { data } = await $request(
                'post',
                'mtexapi/region-service/management/bus/line/query',
                payload
            );
            this.tableData = data.data;
            this.total = data.total;
        },
        handleAction(action, row = {}) {
            // 将action转发到父组件
            this.$emit('action', action, row);
        },
        handleDelete(row) {
            // 删除逻辑
            this.$confirm('确定要删除吗？', '提示', {
                confirmButtonText: '确定',
                cancelButtonText: '取消',
                type: 'warning'
            })
                .then(() => {
                    $request('post', 'mtexapi/region-service/management/bus/line/delete', {
                        ids: [row.lineName]
                    })
                        .then(({ serviceFlag, returnMsg }) => {
                            if (serviceFlag === 'TRUE') {
                                this.refreshTable();
                                this.$message({
                                    type: 'success',
                                    message: '删除成功'
                                });
                            } else {
                                this.$message({
                                    type: 'error',
                                    message: returnMsg
                                });
                            }
                        })
                        .catch(() => {});
                })
                .catch(() => {
                    // 用户取消删除
                });
        },
        refreshTable() {
            this.$refs.busRouteListTable.search();
        }
    },
    mounted() {
        this.refreshTable();
    }
};
</script>
