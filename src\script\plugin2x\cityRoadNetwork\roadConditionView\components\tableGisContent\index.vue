<template>
    <div class="table-gis-content mtex-general-display-board-dark-theme">
        <div class="search-container">
            <searchBar :fields="fields" :form="initialForm">
                <!-- <div class="tools">
                    <el-button
                        type="primary"
                        size="mini"
                        @click="search(1)"
                        :style="{ visibility: showSearchButton ? 'visible' : 'hidden' }"
                        >查询</el-button
                    >
                    <el-button
                        v-if="addButtonText"
                        icon="el-icon-plus"
                        type="primary"
                        size="mini"
                        @click="add"
                        >{{ addButtonText }}</el-button
                    >
                </div> -->
            </searchBar>
        </div>
        <!-- <commonTableContent
            ref="busRouteListTable"
            :fields="fields"
            :initialForm="initialForm"
            addButtonText=""
            :columns="columns"
            :data="tableData"
            :dataTotal="total"
            @get-table-data="handleGetTableData"
            @action="handleAction"
            @delete="handleDelete"
        >
        </commonTableContent> -->
        <div></div>
        <div></div>
    </div>
</template>

<script>
import searchBar from '../../../components/searchBar/index.vue';
import dataTable from '@/script/components/dataTable/index.vue';
import commonTableContent from '../../../components/commonTableContent/index.vue';
import {
    routeFormCols,
    routeTableCols
} from '@/script/constant/cityRoadNetwork/busRouteManager.js';

export default {
    name: 'table-gis-content',
    components: {
        searchBar,
        dataTable,
        commonTableContent
    },
    data() {
        return {
            initialForm: {
                line: undefined,
                lineName: undefined,
                cityName: undefined,
                cityId: ''
            },
            total: 0,
            tableData: []
        };
    },
    computed: {
        fields() {
            return routeFormCols;
        },
        columns() {
            return routeTableCols;
        }
    },
    methods: {
        async handleGetTableData(params, pagination) {
            const payload = {
                line: params.line,
                lineName: params.lineName,
                cityId: params.cityId,
                pageNo: pagination.curPage,
                pageSize: pagination.pageSize
            };

            let { data } = await $request(
                'post',
                'mtexapi/region-service/management/bus/line/query',
                payload
            );
            this.tableData = data.data;
            this.total = data.total;
        },
        handleAction(action, row = {}) {
            // 将action转发到父组件
            this.$emit('action', action, row);
        },
        handleDelete(row) {
            // 删除逻辑
            this.$confirm('确定要删除吗？', '提示', {
                confirmButtonText: '确定',
                cancelButtonText: '取消',
                type: 'warning'
            })
                .then(() => {
                    $request('post', 'mtexapi/region-service/management/bus/line/delete', {
                        ids: [row.lineName]
                    })
                        .then(({ serviceFlag, returnMsg }) => {
                            if (serviceFlag === 'TRUE') {
                                this.refreshTable();
                                this.$message({
                                    type: 'success',
                                    message: '删除成功'
                                });
                            } else {
                                this.$message({
                                    type: 'error',
                                    message: returnMsg
                                });
                            }
                        })
                        .catch(() => {});
                })
                .catch(() => {
                    // 用户取消删除
                });
        },
        refreshTable() {
            this.$refs.busRouteListTable.search();
        }
    },
    mounted() {
        this.refreshTable();
    }
};
</script>
<style lang="less" scoped>
.table-gis-content {
    box-shadow: 0 2px 10px 0 rgba(0, 0, 0, 0.05);
    background: rgba(0, 42, 92, 0.4);
    border-radius: 4px;
    height: 100%;
    display: flex;
    flex-direction: column;
    padding: 18px 20px;

    .search-container {
        height: 44px;

        .tools {
            width: 100%;
            display: flex;
            justify-content: space-between;
        }
    }

    .list-table {
        flex: 1;
        height: 0;
    }
}
</style>
