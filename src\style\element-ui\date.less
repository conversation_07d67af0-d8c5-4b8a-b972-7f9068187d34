.el-date-picker {
    background: #013b70 !important;
    border-radius: 6px !important;
    border: 1px solid #47c0ff !important;
    .el-date-picker__time-header {
        border-bottom: 1px solid #92b9e5;
        .el-input__inner {
            background: #013b70;
            border: 1px solid #47c0ff;
            color: #91aac1;
        }
    }
    .el-picker-panel__content {
        tr {
            color: #92b9e5;
            .prev-month {
                color: rgba(192, 196, 204, 0.5);
            }
        }
        th {
            color: #92b9e5;
            border-bottom: solid 1px #92b9e5;
        }
        td {
            .current {
                background-color: #47c0ff;
            }
            .today {
                color: #47c0ff;
            }
        }
        td:hover {
            color: #47c0ff;
        }
        .el-year-table,
        .el-month-table {
            .cell {
                font-size: 0.78rem;
                color: #91aac1 !important;
            }
            .cell:hover {
                color: #47c0ff !important;
            }
        }
    }
    .el-picker-panel__btn {
        color: #47c0ff !important;
    }
    .el-picker-panel__icon-btn {
        color: #92b9e5;
    }
    .el-date-picker__header-label {
        color: #92b9e5;
    }
    // 时间选择器样式
    .el-time-panel {
        background: #013b70;
        border: 1px solid rgba(255, 255, 255, 0.5);
        .el-time-spinner__item {
            color: #91aac1;
            &:hover {
                background-color: transparent !important;
            }
        }
        .active {
            color: #47c0ff !important;
        }
        .el-time-panel__footer {
            .cancel {
                color: rgba(146, 185, 229, 0.6);
            }
            .confirm {
                color: #47c0ff;
            }
        }
    }
    .el-scrollbar__wrap {
        &::-webkit-scrollbar {
            width: 6px;
            height: 6px;
        }
        &::-webkit-scrollbar-track {
            box-shadow: inset 0 0 5px transparent;
            border-radius: 10px;
            background: transparent;
        }
    }
    &.el-popper[x-placement^='bottom'] .popper__arrow {
        border-bottom-color: #47c0ff;
        &::after {
            border-bottom-color: #013b70;
        }
    }
}
.el-picker-panel__footer {
    border-top: solid 1px #92b9e5 !important;
    background: #013b70 !important;
    //去掉此刻按钮
    .el-button--text {
        display: none !important;
    }
    .el-button--default {
        background: linear-gradient(180deg, #2ab1c7 0%, #105181 48%, #1b90f3 100%);
        opacity: 0.81;
        border-radius: 0.28rem;
        border: 1px solid #47c0ff;
        color: #e8fdff;
    }
}
.el-date-editor {
    .el-input__inner {
        background: #013b70;
        border: 1px solid #47c0ff;
        color: #91aac1;
    }
}
