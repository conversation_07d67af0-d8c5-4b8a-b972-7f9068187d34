<template>
    <el-date-picker
        :append-to-body="false"
        :style="[$attrs.itemStyle || {}]"
        placeholder="请选择时间范围"
        :class="addClass"
        type="daterange"
        format="yyyy-MM-dd HH:mm:ss"
        :value-format="format"
        :picker-options="pickerOptions"
        v-model="currentVal"
        range-separator="至"
        start-placeholder="开始日期"
        end-placeholder="结束日期"
        v-bind="$attrs"
        v-on="$listeners"
        @change="changeInput"
    >
    </el-date-picker>
</template>

<script>
export default {
    name: 'RamsRangeDate',
    model: {
        prop: 'value',
        event: 'change'
    },
    props: {
        value: {
            type: [String, Array],
            default: () => []
        },
        addClass: {
            type: [Object, Array, String]
        },
        pickerOptions: {
            type: Object,
            default: () => ({
                shortcuts: [
                    {
                        text: '最近一周',
                        onClick(picker) {
                            const end = new Date();
                            const start = new Date();
                            start.setTime(start.getTime() - 3600 * 1000 * 24 * 7);
                            picker.$emit('pick', [start, end]);
                        }
                    },
                    {
                        text: '最近一个月',
                        onClick(picker) {
                            const end = new Date();
                            const start = new Date();
                            start.setMonth(start.getMonth() - 1);
                            picker.$emit('pick', [start, end]);
                        }
                    },
                    {
                        text: '最近三个月',
                        onClick(picker) {
                            const end = new Date();
                            const start = new Date();
                            start.setMonth(start.getMonth() - 3);
                            picker.$emit('pick', [start, end]);
                        }
                    }
                ]
            })
        }
    },
    data() {
        return {
            currentVal: this.initCurrentVal(),
            format: 'yyyy-MM-dd'
        };
    },

    watch: {
        value(val) {
            this.currentVal = this.initCurrentVal(val);
        }
    },

    methods: {
        initCurrentVal(val = this.value) {
            if (Array.isArray(val)) {
                return val;
            }
            if (val) {
                return val.split(',');
            }
            return [];
        },
        changeInput(newVal) {
            if (newVal) {
                this.currentVal = newVal;
            }
            let currentVal = this.currentVal ? this.currentVal.join(',') : '';
            this.$emit('change', currentVal);
        }
    }
};
</script>

<style lang="less" scoped></style>
