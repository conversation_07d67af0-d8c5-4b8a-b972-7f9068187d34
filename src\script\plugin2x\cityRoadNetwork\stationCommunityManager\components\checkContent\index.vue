<template>
    <div class="content">
        <Card title="区域小区可视化展示">
            <div class="content-wrapper">
                <mtv-gis
                    class="home-map"
                    ref="mtvGis"
                    :totaloptions="gistotalOptions"
                    @onLoad="gisOnLoad"
                    :autoActive="false"
                ></mtv-gis>
                <!-- 表格 -->
                <dataTable
                    ref="listTable"
                    class="list-table"
                    :columns="columns"
                    :data="tableData"
                    :total="total"
                    :pagination="pagination"
                    :updateTable="getTableData"
                    stripe
                    isHideUpLine
                >
                    <template #indexNo="{ row, inx }">
                        <div>
                            {{ inx + 1 }}
                        </div>
                    </template>
                </dataTable>
            </div>
        </Card>
    </div>
</template>

<script>
import Card from '../../../components/Card/index.vue';
import dataTable from '@/script/components/dataTable/index.vue';
import { communityCheckTableCols } from '@/script/constant/cityRoadNetwork/stationCommunityManager.js';
import { gistotalOptions, getMayType, changeGisColor } from '@/script/constant/gis.js';
import positionMark from '@/img/common/positionMark.png';

export default {
    name: 'check-content',
    components: {
        Card,
        dataTable
    },
    props: {
        row: {
            type: Object,
            default: () => ({})
        }
    },
    computed: {
        columns() {
            return communityCheckTableCols;
        }
    },
    data() {
        return {
            gistotalOptions,
            total: 0,
            pagination: {
                curPage: 1,
                pageSize: 15
            },
            tableData: [],
            zoomList: []
        };
    },
    methods: {
        gisOnLoad() {
            this.dialogGIS = this.$refs.mtvGis.getEntity();
            // 设置底图
            if (getMayType() === 'default') {
                this.dialogGIS.tileLayerList['底图图层'].visible = false;
                //重写getUrl方法
                this.dialogGIS.tileLayerList['高德底图'].getUrl = (x, y, z) => {
                    return `${location.origin}/MapUrl/api/appmaptile?x=${x}&y=${y}&z=${z}`;
                };
            } else {
                this.dialogGIS.tileLayerList['高德底图'] &&
                    (this.dialogGIS.tileLayerList['高德底图'].visible = false);
            }
            changeGisColor(this.dialogGIS);
            this.getTableData();
        },
        drawStation(data = []) {
            if (!data || data.length === 0) {
                return;
            }
            // this.zoomList = this.zoomList.concat(data);
            let layer = new this.dialogGIS.layer();
            layer.name = 'imgTest';
            this.dialogGIS.gis.scene.add(layer.Group);
            layer.visible = true;
            let material = this.dialogGIS.meshList.img.getMaterial({
                url: positionMark,
                opacity: 0.8
            });
            data.autoScale = true;
            let imgMesh = this.dialogGIS.meshList.img.create(data, material);
            layer.add(imgMesh);
            this.dialogGIS.gis.needUpdate = true;
        },
        drawStationOutline() {
            let data = [];
            if (this.row.regionCoors && this.row.regionCoors.startsWith('POLYGON')) {
                data = [this.row.regionCoors.split('((')[1].split('))')[0]];
            } else {
                data = [this.row.regionCoors];
            }
            let ans = [];
            for (let i = 0; i < data.length; i++) {
                let string = data[i];
                let latlngs = string.match(/[^;]+/g);
                let points = [];
                for (let e = 0; e < latlngs.length; e++) {
                    let items = latlngs[e];
                    items = items.match(/[^,]+/g);
                    let lat = Number(items[1]),
                        lng = Number(items[0]);
                    points.push({ lat: lat, lng: lng });
                }
                ans.push({ points: points, color: 0x0095ff });
            }
            ans.width = 3;
            ans.autoScale = true;
            let mesh = this.dialogGIS.meshList.road.create(ans);
            mesh.material.depthTest = false;
            mesh.material.transparent = true;
            this.dialogGIS.gis.scene.add(mesh);

            this.dialogGIS.gis.needUpdate = true;
            this.zoomList = this.zoomList.concat(
                data[0].split(';').map((item) => {
                    return {
                        lat: Number(item.split(',')[1]),
                        lng: Number(item.split(',')[0])
                    };
                })
            );
        },
        async getTableData(paginationData = { ...this.pagination }) {
            let { data } = await $request(
                'post',
                'mtexapi/region-service/station/trainStationLaccell/detail',
                {
                    trainStationId: this.row.trainStationId,
                    pageNo: paginationData.curPage,
                    pageSize: paginationData.pageSize
                }
            );
            this.tableData = data.list;
            this.total = data.total;
            this.drawStation(
                data.list.map((item) => ({
                    ...item,
                    lng: item.longitude,
                    lat: item.latitude,
                    ht: 0.2,
                    width: 30
                }))
            );
            this.drawStationOutline();
            this.dialogGIS.cameraControl.zoomByPoints(this.zoomList, 1.2);
        }
    }
};
</script>

<style lang="less" scoped>
.content {
    min-height: 100%;
    height: max-content;
    display: flex;
    flex-direction: column;

    .content-wrapper {
        display: flex;
        flex-direction: column;
        justify-content: center;
        align-items: center;
        padding: 16px 24px 24px 24px;

        .home-map {
            border-radius: 2px;
            border: 1px solid rgba(64, 155, 255, 0.3);
            width: 100%;
            height: 500px;
            margin-bottom: 16px;
        }

        .list-table {
            width: 100%;
            height: 500px;
        }
    }
}
</style>
