<template>
    <div class="content">
        <Card title="高速道路小区可视化展示">
            <div class="content-wrapper">
                <mtv-gis
                    class="home-map"
                    ref="mtvGis"
                    :totaloptions="gistotalOptions"
                    @onLoad="gisOnLoad"
                    :autoActive="false"
                ></mtv-gis>
                <!-- 表格 -->
                <dataTable
                    ref="listTable"
                    class="list-table"
                    :columns="columns"
                    :data="tableData"
                    :total="total"
                    :pagination="pagination"
                    :updateTable="getTableData"
                    stripe
                    isHideUpLine
                >
                    <template #indexNo="{ row, inx }">
                        <div>
                            {{ inx + 1 }}
                        </div>
                    </template>
                    <template #lastUpdateTime="{ row }">
                        <div>
                            {{ formatTime(row.lastUpdateTime) }}
                        </div>
                    </template>
                </dataTable>
            </div>
        </Card>
    </div>
</template>

<script>
import Card from '../../../components/Card/index.vue';
import dataTable from '@/script/components/dataTable/index.vue';
import { communityCheckTableCols } from '@/script/constant/cityRoadNetwork/highwayCommunityRelationManager.js';
import { gistotalOptions, getMayType, changeGisColor } from '@/script/constant/gis.js';
import { formatTime } from '@/script/utils/method.js';

export default {
    name: 'check-content',
    components: {
        Card,
        dataTable
    },
    props: {
        row: {
            type: Object,
            default: () => ({})
        }
    },
    computed: {
        columns() {
            return communityCheckTableCols;
        }
    },
    data() {
        return {
            formatTime,
            gistotalOptions,
            total: 0,
            pagination: {
                curPage: 1,
                pageSize: 15
            },
            tableData: []
        };
    },
    methods: {
        gisOnLoad() {
            this.dialogGIS = this.$refs.mtvGis.getEntity();
            // 设置底图
            if (getMayType() === 'default') {
                this.dialogGIS.tileLayerList['底图图层'].visible = false;
                //重写getUrl方法
                this.dialogGIS.tileLayerList['高德底图'].getUrl = (x, y, z) => {
                    return `${location.origin}/MapUrl/api/appmaptile?x=${x}&y=${y}&z=${z}`;
                };
            } else {
                this.dialogGIS.tileLayerList['高德底图'] &&
                    (this.dialogGIS.tileLayerList['高德底图'].visible = false);
            }
            changeGisColor(this.dialogGIS);
            this.getTableData();
            this.getRoadInfo();
        },
        async getTableData(paginationData = { ...this.pagination }) {
            let { data } = await $request(
                'post',
                'mtexapi/region-service/regionsubscribe/highSpeedEciDetails/getRoadECIDetail',
                {
                    eciIds: this.row.eciIds,
                    pageNo: paginationData.curPage,
                    pageSize: paginationData.pageSize
                }
            );
            this.tableData = data.list;
            this.total = data.total;
        },
        getRoadInfo() {
            $request(
                'post',
                'mtexapi/region-service/regionsubscribe/highWayDetails/getRoadDetail',
                {
                    roadSubIds: this.row.roadSubIds
                }
            ).then(({ data: res }) => {
                this.drawRoad(res);
            });
        },
        drawRoad(data) {
            if (!data || data.length === 0) {
                return;
            }
            //准备数据
            //高度间隔，处理重叠,视情况加大
            let htStep = 0.0001;
            let datas = data.map((item) => {
                return {
                    points: [
                        {
                            lat: Number(item.startLatLng.split(',')[0]),
                            lng: Number(item.startLatLng.split(',')[1]),
                            ht: htStep * 0
                        },
                        {
                            lat: Number(item.endLatLng.split(',')[0]),
                            lng: Number(item.endLatLng.split(',')[1]),
                            ht: htStep * 0
                        }
                    ],
                    color: 0x00ff00
                };
            });
            //创建图层
            let layer = new this.dialogGIS.layer();
            layer.visible = true;
            this.dialogGIS.gis.scene.add(layer.Group);

            //开启保持道路像素大小
            datas.autoScale = true;
            //道路宽度
            datas.width = 5;
            //创建道路模型
            let roadMesh = this.dialogGIS.meshList.road.create(datas);
            //模型添加进图层
            layer.add(roadMesh);
            //更新GIS
            this.dialogGIS.gis.needUpdate = true;
            const regionCoorsList = data.map((item) => {
                return {
                    lat: Number(item.startLatLng.split(',')[0]),
                    lng: Number(item.startLatLng.split(',')[1])
                };
            });
            this.dialogGIS.cameraControl.zoomByPoints(regionCoorsList, 1.2);
        }
    }
};
</script>

<style lang="less" scoped>
.content {
    min-height: 100%;
    height: max-content;
    display: flex;
    flex-direction: column;

    .content-wrapper {
        display: flex;
        flex-direction: column;
        justify-content: center;
        align-items: center;
        padding: 16px 24px 24px 24px;

        .home-map {
            border-radius: 2px;
            border: 1px solid rgba(64, 155, 255, 0.3);
            width: 100%;
            height: 500px;
            margin-bottom: 16px;
        }

        .list-table {
            width: 100%;
            height: 500px;
        }
    }
}
</style>
