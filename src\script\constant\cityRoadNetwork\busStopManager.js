import { SHANDONG_CITY } from '@/script/constant/shandong';
const stopFormCols = [
    {
        prop: 'stationName',
        label: '站点名称',
        element: 'el-input',
        attrs: {
            placeholder: '请输入',
            clearable: true
        },
        span: 4
    },
    {
        prop: 'cityId',
        label: '归属城市',
        element: 'el-select',
        attrs: {
            placeholder: '请选择',
            clearable: true,
            'popper-class': 'gdb-select-dropdown-dark gdb-popover-dark'
        },
        slot: {
            element: 'el-option',
            enums: SHANDONG_CITY()
        },
        span: 4
    },
    {
        span: 16
    }
];

const stopTableCols = [
    {
        prop: 'stationName',
        label: '公交站点名称'
    },
    {
        prop: 'cityName',
        label: '归属城市'
    },
    {
        prop: 'line',
        label: '归属线路'
    },
    {
        prop: 'lng_lat',
        label: '站点经纬度',
        'min-width': 180
    },
    {
        prop: 'updateTime',
        label: '最后更新时间',
        'min-width': 180
    },
    {
        prop: 'updateUser',
        label: '最后更新用户'
    },
    {
        prop: 'operation',
        label: '操作',
        width: 180
    }
];

export { stopFormCols, stopTableCols };
