<template>
    <div class="content">
        <Card title="高速道路基础信息">
            <div class="form-wrapper">
                <BaseForm
                    ref="baseForm"
                    :form-data="formData"
                    :form-config="formConfig"
                    :rules="rules"
                    :action="action"
                    :visible-fields="nowFields"
                    @submit="handleSubmit"
                >
                </BaseForm>
            </div>
        </Card>
    </div>
</template>

<script>
import Card from '../../../components/Card/index.vue';
import BaseForm from '../../../components/BaseForm/index.vue';
import { SHANDONG_CITY } from '@/script/constant/shandong';
import { getLabelByValue } from '@/script/utils/method.js';

export default {
    name: 'HighwayDetailContent',
    components: {
        Card,
        BaseForm
    },
    props: {
        row: {
            type: Object,
            default: () => ({})
        },
        action: {
            type: String,
            default: 'check'
        }
    },
    data() {
        return {
            nowFields: {
                add: [],
                edit: ['roadName'],
                check: ['cityName', 'roadName']
            },
            formData: {
                cityName: '',
                roadName: ''
            },
            formConfig: [
                {
                    prop: 'cityName',
                    label: '地市名称：',
                    type: 'el-select',
                    placeholder: '请选择地市名称',
                    options: SHANDONG_CITY(),
                    attrs: {
                        clearable: true,
                        'popper-class': 'gdb-select-dropdown-dark gdb-popover-dark'
                    },
                    formatter: (value) => getLabelByValue(SHANDONG_CITY(), value)
                },
                {
                    prop: 'roadName',
                    label: '高速道路名称：',
                    type: 'input',
                    placeholder: '请输入道路现用正式名称',
                    attrs: {
                        clearable: true
                    }
                }
            ],
            rules: {
                cityName: [{ required: true, message: '请选择地市名称', trigger: 'change' }],
                roadName: [{ required: true, message: '请输入高速道路名称', trigger: 'blur' }]
            }
        };
    },
    created() {
        if (['edit', 'check'].includes(this.action) && this.row) {
            this.formData = { ...this.formData, ...this.row };
        }
    },
    methods: {
        handleSubmit(form, modeText) {
            // 只需更改apiPathMap接口路径以及添加payload
            const apiPathMap = new Map([
                // ['add', 'highway/community/add'], // 没有新增，变为导入了
                ['edit', 'regionsubscribe/highWayDetails/updateRoadInfo']
            ]);

            const payload = {
                roadSubIds: this.row.roadSubIds,
                newRoadName: form.roadName
            };

            $request('post', `mtexapi/region-service/${apiPathMap.get(this.action)}`, payload).then(
                ({ serviceFlag, returnMsg }) => {
                    if (serviceFlag === 'TRUE') {
                        this.$emit('goBack');
                        this.$message({
                            type: 'success',
                            message: `${modeText}成功`
                        });
                        return;
                    }
                    this.$message({
                        type: 'error',
                        message: returnMsg
                    });
                }
            );
        }
    }
};
</script>

<style lang="less" scoped>
.content {
    min-height: 100%;
    height: max-content;
    display: flex;
    flex-direction: column;

    .form-wrapper {
        display: flex;
        justify-content: center;
        align-items: center;
    }
}
</style>
