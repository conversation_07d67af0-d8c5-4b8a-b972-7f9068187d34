<template>
    <div class="content-container">
        <mtv-gis :ref="gisId" :totaloptions="gisOptions" @onLoad="gisOnLoad"></mtv-gis>
        <div class="timeline-container">
            <div class="date-axis">
                <div class="text">日期轴：</div>
                <i class="el-icon-arrow-left"></i>
                <div class="date-box">
                    <div
                        v-for="(date, index) in dates"
                        :key="'date-' + index"
                        class="date-item"
                        @click="chooseDate(date)"
                    >
                        <div class="date-text">{{ date }}</div>
                        <div
                            class="date-point"
                            :class="{ 'data-point-active': dateActive == date }"
                        ></div>
                    </div>
                </div>

                <i class="el-icon-arrow-right"></i>
            </div>
        </div>
    </div>
</template>

<script>
import { gistotalOptions, getMayType, changeGisColor } from '@/script/constant/gis.js';
export default {
    props: {
        resultData: {
            type: Array,
            default: () => []
        }
    },
    watch: {
        resultData: {
            handler(newVal) {
                console.log(newVal);

                // this.getChartData(newVal);
            },
            deep: true
        }
    },
    data() {
        return {
            dialogGIS: null,
            gisId: 'gisId',
            gisOptions: gistotalOptions,
            dateActive: 0,
            dates: ['02.18', '02.19', '02.20', '02.21', '02.22', '02.23', '02.24'],
            times: ['0', '1', '2', '3', '4', '5', '6'],
            timeActive: 0
        };
    },
    methods: {
        gisOnLoad() {
            this.dialogGIS = this.$refs[this.gisId].getEntity();
            // 设置底图
            if (getMayType() === 'default') {
                this.dialogGIS.tileLayerList['底图图层'].visible = false;
                //重写getUrl方法
                this.dialogGIS.tileLayerList['高德底图'].getUrl = (x, y, z) => {
                    return `${location.origin}/MapUrl/api/appmaptile?x=${x}&y=${y}&z=${z}`;
                };
            } else {
                this.dialogGIS.tileLayerList['高德底图'] &&
                    (this.dialogGIS.tileLayerList['高德底图'].visible = false);
            }
            changeGisColor(this.dialogGIS);
        },
        chooseDate(date) {
            this.dateActive = date;
        },
        chooseTime(time) {
            this.timeActive = time;
        }
    }
};
</script>

<style lang="less" scoped>
.content-container {
    width: 100%;
    height: 55vh;
    .mtv-gis {
        width: 100%;
        height: 23.33rem;
        margin: 1.11rem0;
        border-radius: 0.22rem;
        overflow: hidden;
    }
    .timeline-container {
        position: relative;
        margin-top: 2.22rem;
        .date-axis {
            position: relative;
            height: 2.22rem;
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-top: 1.11rem;
            .text {
                font-size: 0.83rem;
                color: #686e80;
                width: 3.89rem;
            }
            .el-icon-arrow-left {
                width: 0.56rem;
                color: #d8d8d8;
                margin-right: 0.56rem;
            }
            .el-icon-arrow-right {
                width: 0.56rem;
                font-weight: bold;
                color: #d8d8d8;
                margin-left: 1.11rem;
            }
            .date-box {
                width: calc(100% - 0.56rem);
                display: flex;
                .date-item {
                    position: relative;
                    z-index: 2;
                    flex: 1;
                    border-bottom: 2px dashed #e6e8eb;
                    .date-point {
                        width: 0.72rem;
                        height: 0.72rem;
                        border-radius: 50%;
                        border: 0.11rem solid #d8d8d8;
                        background: #fff;
                        position: absolute;
                        bottom: -8px;
                        cursor: pointer;
                    }
                    .date-point:hover,
                    .data-point-active {
                        border-color: #4986ff;
                    }

                    .date-text {
                        min-width: 2.22rem;
                        position: absolute;
                        top: -1.67rem;
                        left: -0.56rem;
                        font-size: 0.83rem;
                        color: #666;
                    }
                }
                .date-item:last-child {
                    flex: 0;
                }
            }
        }
    }
}
</style>
