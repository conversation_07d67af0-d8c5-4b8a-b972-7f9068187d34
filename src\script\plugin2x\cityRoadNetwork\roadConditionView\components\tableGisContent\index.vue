<template>
    <div class="table-gis-content mtex-general-display-board-dark-theme">
        <div class="search-container">
            <searchBar :fields="fields" :form="form">
                <div class="tools">
                    <el-button type="primary" size="mini" @click="search(1)">查询</el-button>
                </div>
            </searchBar>
        </div>
        <div class="content-grid">
            <!-- 左侧表格区域 -->
            <div class="table-section">
                <dataTable
                    ref="listTable"
                    class="list-table"
                    :columns="columns"
                    :data="tableData"
                    :total="total"
                    :layout="'total, prev, pager, next, sizes, jumper'"
                    :pagination="pagination"
                    :updateTable="getTableData"
                    stripe
                    isHideUpLine
                    @row-click="onRowClick"
                >
                    <!-- 默认操作按钮 -->
                    <template #operation="{ row }">
                        <el-button class="p-0" type="text" @click="check(row)">详情</el-button>
                        <el-button class="p-0" type="text" @click="edit(row)">修改</el-button>
                        <el-button class="p-0" type="text" @click="del(row)">删除</el-button>
                    </template>
                </dataTable>
            </div>
            <!-- 右侧地图区域 -->
            <div class="gis-section">
                <mtv-gis
                    class="road-condition-map"
                    ref="mtvGis"
                    :totaloptions="gistotalOptions"
                    @onLoad="gisOnLoad"
                    :autoActive="false"
                ></mtv-gis>
                <!-- 时间轴组件 -->
                <hourlyTimeline
                    ref="timeline"
                    :initialHour="currentHour"
                    @hour-change="onHourChange"
                />
                <!-- 图例 -->
                <div class="road-condition-legend">
                    <p>图例</p>
                    <div class="legend-content">
                        <div class="legend-item" v-for="item in legendData" :key="item.name">
                            <span class="legend-color" :style="{ '--color': item.color }"></span>
                            <span class="legend-name">{{ item.name }}</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</template>

<script>
import searchBar from '../../../components/searchBar/index.vue';
import dataTable from '@/script/components/dataTable/index.vue';
import hourlyTimeline from '../hourlyTimeline/index.vue';
import { gistotalOptions, getMayType, changeGisColor } from '@/script/constant/gis.js';
import {
    roadConditionFormCols,
    roadConditionTableCols
} from '@/script/constant/cityRoadNetwork/roadConditionView.js';
import dayjs from 'dayjs';

export default {
    name: 'table-gis-content',
    components: {
        searchBar,
        dataTable,
        hourlyTimeline
    },
    data() {
        return {
            form: {
                roadName: undefined,
                cityId: '',
                analysisTime: undefined
            },
            total: 0,
            tableData: [],
            pagination: {
                curPage: 1,
                pageSize: 15
            },
            gistotalOptions,
            dialogGIS: null,
            currentHour: 1,
            selectedRoadData: null,
            roadLayer: null,
            legendData: [
                { name: '畅通', color: '#25D725' },
                { name: '基本畅通', color: '#C4FF4E' },
                { name: '拥堵', color: '#D7A225' },
                { name: '严重拥堵', color: '#EE3E23' }
            ]
        };
    },
    computed: {
        fields() {
            return roadConditionFormCols;
        },
        columns() {
            return roadConditionTableCols;
        }
    },
    methods: {
        search(pageIndex = 1) {
            this.pagination.curPage = pageIndex;
            this.$refs.listTable.scrollToTop();
            this.getTableData(this.pagination);
        },
        getTableData(paginationData = {}) {
            this.handleGetTableData(this.form, paginationData);
        },
        async handleGetTableData(_, pagination) {
            // const payload = {
            //     roadName: params.roadName,
            //     cityId: params.cityId,
            //     analysisTime: params.analysisTime,
            //     pageNo: pagination.curPage,
            //     pageSize: pagination.pageSize
            // };

            // 实际项目中需要替换为真实的API
            // let { data } = await $request(
            //     'post',
            //     'mtexapi/region-service/management/road/condition/query',
            //     payload
            // );
            // 模拟数据
            const data = {
                data: Array.from({ length: 96 }, (_, i) => ({
                    id: i + 1,
                    roadName: '济南路' + (i + 1),
                    analysisTime: dayjs(new Date()).format('YYYY-MM-DD'),
                    personFlow: Math.floor(Math.random() * 1000),
                    roadCondition: '畅通',
                    // 模拟道路坐标数据
                    roadSegments: [
                        {
                            startLatLng: `${36.6507 + Math.random() * 0.1},${117.114 + Math.random() * 0.1}`,
                            endLatLng: `${36.6507 + Math.random() * 0.1},${117.114 + Math.random() * 0.1}`
                        }
                    ]
                })).slice(
                    (pagination.curPage - 1) * pagination.pageSize,
                    pagination.curPage * pagination.pageSize
                ),
                total: 96
            };
            this.tableData = data.data;
            this.total = data.total;

            // 自动选择第一条数据并绘制道路
            if (data.data.length > 0 && !this.selectedRoadData) {
                this.selectRoadData(data.data[0]);
            }
        },
        gisOnLoad() {
            this.dialogGIS = this.$refs.mtvGis.getEntity();
            // 设置底图
            if (getMayType() === 'default') {
                this.dialogGIS.tileLayerList['底图图层'].visible = false;
                //重写getUrl方法
                this.dialogGIS.tileLayerList['高德底图'].getUrl = (x, y, z) => {
                    return `${location.origin}/MapUrl/api/appmaptile?x=${x}&y=${y}&z=${z}`;
                };
            } else {
                this.dialogGIS.tileLayerList['高德底图'] &&
                    (this.dialogGIS.tileLayerList['高德底图'].visible = false);
            }
            changeGisColor(this.dialogGIS);

            // 初始化地图数据
            this.initMapData();

            // 如果已有选中的道路数据，绘制道路
            if (this.selectedRoadData) {
                this.drawRoad(this.selectedRoadData);
            }
        },
        initMapData() {
            // 在地图上显示路况数据
            // 这里可以根据实际需求添加路况可视化逻辑
        },
        drawRoad(roadData) {
            if (!roadData || !roadData.roadSegments || roadData.roadSegments.length === 0) {
                return;
            }

            // 清除之前的道路图层
            this.clearRoadLayer();

            // 准备数据
            let htStep = 0.0001;
            let datas = roadData.roadSegments.map((item) => {
                return {
                    points: [
                        {
                            lat: Number(item.startLatLng.split(',')[0]),
                            lng: Number(item.startLatLng.split(',')[1]),
                            ht: htStep * 0
                        },
                        {
                            lat: Number(item.endLatLng.split(',')[0]),
                            lng: Number(item.endLatLng.split(',')[1]),
                            ht: htStep * 0
                        }
                    ],
                    color: this.getRoadColor(roadData.roadCondition)
                };
            });

            // 创建图层
            this.roadLayer = new this.dialogGIS.layer();
            this.roadLayer.visible = true;
            this.dialogGIS.gis.scene.add(this.roadLayer.Group);

            // 开启保持道路像素大小
            datas.autoScale = true;
            // 道路宽度
            datas.width = 5;
            // 创建道路模型
            let roadMesh = this.dialogGIS.meshList.road.create(datas);
            // 模型添加进图层
            this.roadLayer.add(roadMesh);
            // 更新GIS
            this.dialogGIS.gis.needUpdate = true;

            // 缩放到道路区域
            const regionCoorsList = roadData.roadSegments.map((item) => {
                return {
                    lat: Number(item.startLatLng.split(',')[0]),
                    lng: Number(item.startLatLng.split(',')[1])
                };
            });
            this.dialogGIS.cameraControl.zoomByPoints(regionCoorsList, 1.2);
        },
        clearRoadLayer() {
            if (this.roadLayer) {
                this.dialogGIS.gis.scene.remove(this.roadLayer.Group);
                this.roadLayer = null;
                this.dialogGIS.gis.needUpdate = true;
            }
        },
        getRoadColor(condition) {
            const colorMap = {
                畅通: 0x25d725,
                基本畅通: 0xc4ff4e,
                拥堵: 0xd7a225,
                严重拥堵: 0xee3e23
            };
            return colorMap[condition] || 0x25d725;
        },
        selectRoadData(roadData) {
            this.selectedRoadData = roadData;
            if (this.dialogGIS) {
                this.drawRoad(roadData);
            }
            // 重置时间轴到第1小时
            this.currentHour = 1;
            if (this.$refs.timeline) {
                this.$refs.timeline.resetToHour(1);
            }
        },
        onHourChange(hour) {
            this.currentHour = hour;
            // 根据选中的小时更新人流量数据显示
            this.updateTrafficData(hour);
        },
        updateTrafficData(hour) {
            // 模拟根据小时更新人流量数据
            if (this.selectedRoadData) {
                // 这里可以根据实际需求更新地图上的人流量显示
                console.log(`更新第${hour}小时的人流量数据`);
            }
        },
        check(row) {
            this.$emit('action', 'check', row);
        },
        edit(row) {
            this.$emit('action', 'edit', row);
        },
        del(row) {
            this.$confirm('确定要删除吗？', '提示', {
                confirmButtonText: '确定',
                cancelButtonText: '取消',
                type: 'warning'
            })
                .then(() => {
                    $request('post', 'mtexapi/region-service/management/road/condition/delete', {
                        ids: [row.id]
                    })
                        .then(({ serviceFlag, returnMsg }) => {
                            if (serviceFlag === 'TRUE') {
                                this.refreshTable();
                                this.$message({
                                    type: 'success',
                                    message: '删除成功'
                                });
                            } else {
                                this.$message({
                                    type: 'error',
                                    message: returnMsg
                                });
                            }
                        })
                        .catch(() => {});
                })
                .catch(() => {
                    // 用户取消删除
                });
        },
        refreshTable() {
            this.$refs.listTable && this.search();
        },
        onRowClick(row) {
            // 用户点击表格行时重新绘制选中道路
            this.selectRoadData(row);
        }
    },
    mounted() {
        this.refreshTable();
    },
    beforeDestroy() {
        // 清理资源
        this.clearRoadLayer();
    }
};
</script>
<style lang="less" scoped>
.table-gis-content {
    box-shadow: 0 2px 10px 0 rgba(0, 0, 0, 0.05);
    background: rgba(0, 42, 92, 0.4);
    border-radius: 4px;
    height: 100%;
    display: flex;
    flex-direction: column;
    padding: 18px 20px;

    .search-container {
        height: 44px;
        margin-bottom: 16px;

        .tools {
            width: 100%;
            display: flex;
            justify-content: space-between;
        }
    }

    .content-grid {
        flex: 1;
        height: 0;
        display: flex;
        gap: 16px;

        .table-section {
            min-width: 0;
            flex: 4;
            display: flex;
            flex-direction: column;
            border-radius: 4px;

            .list-table {
                flex: 1;
                height: 0;
            }
        }

        .gis-section {
            min-width: 0;
            flex: 6;
            display: flex;
            flex-direction: column;
            border-radius: 4px;
            position: relative;

            .road-condition-map {
                flex: 1;
                height: 100%;
                border-radius: 4px;
                overflow: hidden;
            }
            .road-condition-timeline {
                position: absolute;
                left: 60px;
                right: 60px;
                bottom: 12px;
                // width: 132px;
                height: 24px;
                display: flex;
                align-items: center;
                gap: 12px;
                .label-name {
                    background-image: url('~@/img/cityRoadNet/select-bg.png');
                    background-size: 100% 100%;
                    background-repeat: no-repeat;
                    padding: 0 10px;
                    line-height: 24px;
                    font-size: 14px;
                    color: #fff;
                    text-align: center;
                    user-select: none;
                }
                .timeline-axis {
                    min-width: 0;
                    flex: 1;
                    height: 100%;
                    background-image: url('~@/img/cityRoadNet/time-axis.png');
                    background-size: 98% 100%;
                    background-repeat: no-repeat;
                    background-position: center;
                    user-select: none;
                    position: relative;

                    .arrow-left,
                    .arrow-right {
                        position: absolute;
                        top: 50%;
                        transform: translateY(-50%);
                        width: 18px;
                        height: 28px;
                        background-size: 100% 100%;
                        background-repeat: no-repeat;
                        cursor: pointer;
                    }
                    .arrow-left {
                        left: 0;
                        background-image: url('~@/img/cityRoadNet/arrow-left.png');
                    }
                    .arrow-right {
                        right: 0;
                        background-image: url('~@/img/cityRoadNet/arrow-right.png');
                    }

                    .time-axis {
                        width: 100%;
                        height: 100%;
                        padding: 0 24px;
                        display: flex;
                        justify-content: space-between;
                        .hour-item {
                            min-width: 0;
                            max-width: 24px;
                            flex-shrink: 1;
                            display: flex;
                            justify-content: center;
                            width: 24px;
                            height: 24px;
                            background-image: url('~@/img/cityRoadNet/blue-point.png');
                            background-size: 100% 100%;
                            background-repeat: no-repeat;
                            cursor: pointer;
                            user-select: none;
                            position: relative;
                            .time-text {
                                position: absolute;
                                top: 0;
                                transform: translateY(-125%);
                                font-family: SourceHanSansCN-Medium, SourceHanSansCN-Medium;
                                font-weight: normal;
                                font-size: 11px;
                                color: #acb5cc;
                                line-height: 12px;
                                text-align: center;
                                font-style: normal;
                                text-transform: none;
                            }
                            &.active-hour {
                                background-image: url('~@/img/cityRoadNet/orange-point.png');
                                .time-text {
                                    color: #5df2ff;
                                }
                            }
                        }
                    }
                }
            }
            .road-condition-legend {
                position: absolute;
                right: 72px;
                bottom: 90px;
                width: 132px;
                height: fit-content;
                border: 1px solid #47c0ffff;
                background-color: #013b70bd;
                border-radius: 7px;
                padding: 12px 24px;
                display: flex;
                flex-direction: column;
                p {
                    font-family:
                        Source Han Sans CN,
                        Source Han Sans CN;
                    font-weight: 500;
                    font-size: 11px;
                    color: #ffffff;
                    margin-bottom: 6px;
                }
                .legend-content {
                    min-height: 0;
                    flex: 1;
                    display: flex;
                    flex-direction: column;
                    gap: 4px;
                    .legend-item {
                        display: flex;
                        align-items: center;
                        gap: 8px;
                    }
                    .legend-color {
                        display: block;
                        --color: #25d725;
                        width: 12px;
                        height: 0px;
                        border: 2px solid var(--color);
                    }
                    .legend-name {
                        font-family:
                            Source Han Sans CN,
                            Source Han Sans CN;
                        font-weight: 400;
                        font-size: 12px;
                        color: #c9dfff;
                    }
                }
            }
        }
    }
}

.p-0 {
    padding: 0;
}

/deep/ .el-button {
    &--text {
        color: #0095ff;
        &:hover {
            color: #fff;
        }
    }
}
</style>
