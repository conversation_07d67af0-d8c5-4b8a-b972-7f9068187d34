<template>
    <div class="input-tag-container">
        <div class="tags-wrapper" @click="handleWrapperClick">
            <div class="tags-row">
                <el-tag
                    v-for="(tag, index) in tags"
                    :key="index"
                    type="info"
                    size="mini"
                    :closable="!readonly"
                    :disable-transitions="false"
                    @close="handleClose(index)"
                    class="tag-item"
                    @click.stop
                >
                    {{ tag }}
                </el-tag>
            </div>
            <el-input
                v-if="!readonly"
                ref="tagInput"
                v-model="inputValue"
                class="input-new-tag"
                :placeholder="placeholder"
                @keyup.enter.native="handleInputConfirm"
                @blur="handleInputConfirm"
                @click.stop
            ></el-input>
        </div>
    </div>
</template>

<script>
export default {
    name: 'InputToTag',
    props: {
        // 标签数组，支持v-model双向绑定
        value: {
            type: Array,
            default: () => []
        },
        // 输入框占位文本
        placeholder: {
            type: String,
            default: '请在完成一个标签的输入后使用回车键分隔'
        },
        // 最大允许的标签数量
        maxTags: {
            type: Number,
            default: Infinity
        },
        readonly: {
            type: Boolean,
            default: false
        }
    },
    data() {
        return {
            tags: this.value,
            inputValue: ''
        };
    },
    watch: {
        value: {
            handler(newVal) {
                this.tags = newVal;
            },
            deep: true
        }
    },
    methods: {
        handleClose(index) {
            if (this.readonly) return;
            this.tags.splice(index, 1);
            this.$emit('input', this.tags);
            this.$emit('change', this.tags);
        },
        handleInputConfirm() {
            const inputValue = this.inputValue.trim();
            if (inputValue) {
                if (this.tags.length >= this.maxTags) {
                    this.$message.warning(`最多只能添加${this.maxTags}个标签`);
                    this.inputValue = '';
                    return;
                }
                if (this.tags.includes(inputValue)) {
                    this.$message.warning('标签已存在');
                    this.inputValue = '';
                    return;
                }
                this.tags.push(inputValue);
                // input事件用于实现v-model双向绑定
                this.$emit('input', this.tags);
                // change事件用于通知父组件标签发生变化
                this.$emit('change', this.tags);
            }
            this.inputValue = '';
        },
        handleWrapperClick(e) {
            if (this.readonly) return;
            // 如果点击的是wrapper本身（而不是其中的标签或输入框）
            if (e.target === e.currentTarget) {
                this.$refs.tagInput.focus();
            }
        }
    }
};
</script>

<style lang="less" scoped>
.input-tag-container {
    width: 100%;

    .tags-wrapper {
        display: flex;
        flex-direction: column;
        align-items: flex-start;
        padding: 8px 8px;
        border: 1px solid rgba(18, 139, 207, 0.6);
        border-radius: 2px;
        background-color: rgba(4, 52, 106, 0.6);
        min-height: 84px;

        .tags-row {
            display: flex;
            flex-wrap: wrap;
            gap: 4px;
            width: 100%;

            .tag-item {
                background-color: rgba(18, 139, 207, 0.6);
                border: none;
                color: #fff;
                font-family: SourceHanSansCN, SourceHanSansCN;
                // font-weight: bold;
            }
        }

        .input-new-tag {
            width: 100%;
            margin: 2px 0;
            line-height: 28px;

            :deep(.el-input__inner) {
                border: none;
                padding: 0;
                height: 28px;
                line-height: 28px;
                background-color: transparent;
                &:focus {
                    outline: none;
                    box-shadow: none;
                }
            }

            :deep(.el-input__wrapper) {
                box-shadow: none !important;
                padding: 0 !important;
            }
        }
    }
}
</style>
