<template>
    <el-dialog
        custom-class="import-content"
        title="导入"
        :visible.sync="dialogVisible"
        width="30%"
        top="30vh"
    >
        <div class="info-upload-wrapper">
            <el-upload
                class="info-upload"
                drag
                :auto-upload="false"
                :on-change="handleFileChange"
                action=""
                :limit="1"
                :on-exceed="handleExceed"
                :before-remove="handleBeforeRemove"
                :file-list="fileList"
                accept=".xls,.xlsx"
                ref="upload"
            >
                <i class="el-icon-upload"></i>
                <div class="el-upload__text">将文件拖到此处，或<em>点击上传</em></div>
                <div class="el-upload__tip" slot="tip">只能上传.xls/.xlsx文件</div>
            </el-upload>
        </div>
        <span slot="footer" class="dialog-footer">
            <el-button
                class="template-btn"
                size="small"
                type="primary"
                :loading="loading"
                @click="downloadTemplate"
                >下载模板
            </el-button>
            <el-button class="cancel-btn" size="small" @click="handleCancel">取 消 </el-button>
            <el-button class="confirm-btn" size="small" type="primary" @click="handleConfirm"
                >确 定
            </el-button>
        </span>
    </el-dialog>
</template>

<script>
export default {
    name: 'import-content',
    props: {
        contentVisible: {
            type: Boolean,
            default: false
        },
        loading: {
            type: Boolean,
            default: false
        }
    },
    data() {
        return {
            formData: {
                importFile: ''
            },
            fileList: []
        };
    },
    computed: {
        dialogVisible: {
            get() {
                return this.contentVisible;
            },
            set(newVal) {
                this.$emit('update:contentVisible', newVal);
            }
        }
    },
    methods: {
        handleExceed() {
            this.$message.warning('只能上传一个文件');
        },
        // 文件选择变化
        handleFileChange(file) {
            // 验证文件类型
            const fileName = file.name;
            const fileExt = fileName.substring(fileName.lastIndexOf('.') + 1).toLowerCase();
            if (!['xls', 'xlsx'].includes(fileExt)) {
                this.$message.error('只能上传.xls/.xlsx格式的文件');
                this.$refs.upload.clearFiles();
                return;
            }
            this.fileList = [file];
        },
        // 文件移除前的钩子
        handleBeforeRemove(file, fileList) {
            this.fileList = [];
            return true;
        },
        // 取消操作
        handleCancel() {
            this.dialogVisible = false;
            this.fileList = [];
            this.$refs.upload.clearFiles();
        },
        // 确认导入
        handleConfirm() {
            if (this.fileList.length === 0) {
                this.$message.warning('请选择要上传的文件');
                return;
            }
            this.$emit('confirm', { file: this.fileList[0].raw });
            this.dialogVisible = false;
            this.fileList = [];
            this.$refs.upload.clearFiles();
        },
        // 下载模板
        downloadTemplate() {
            this.$emit('download');
        }
    }
};
</script>

<style lang="less" scoped>
/deep/ .import-content {
    background-color: #093e79;
    .el-dialog__title {
        color: #fff;
    }
    .info-upload-wrapper {
        width: 100%;
        height: 100%;
        display: flex;
        justify-content: center;
        align-items: center;
    }
    .dialog-footer {
        display: flex;
        justify-content: flex-end;
        .template-btn {
            margin-right: auto;
        }
        .cancel-btn {
            background-color: transparent !important;
            border: 1px solid rgba(255, 255, 255, 0.2) !important;
            color: #fff !important;
        }
        .confirm-btn {
            color: #fff !important;
        }
    }
}
.info-upload {
    width: max-content;
    /deep/ .el-upload {
        &-dragger {
            background-color: #093e79 !important;
            border: 1px dashed #409eff;
            &:hover {
                border-color: #c9dfff !important;
            }
            .el-upload__text {
                color: #fff !important;
            }
        }
        &__input {
            display: none !important;
        }
        &__tip {
            margin: 0;
            color: rgba(255, 255, 255, 0.45);
        }
        &-list__item {
            .el-icon-document {
                color: rgba(255, 255, 255, 0.85);
            }

            .el-icon-close {
                color: #409eff;
            }

            .el-icon-close-tip {
                display: none;
            }

            .el-progress__text {
                color: rgba(255, 255, 255, 0.85);
            }

            &-name {
                color: rgba(255, 255, 255, 0.85);
            }

            &:hover {
                background-color: #093e79;
            }
        }
    }
}
</style>
