<template>
    <div class="poi-content">
        <div class="poi-list">
            <div v-for="(poi, index) in pois" :key="index" class="poi-item">
                <el-input
                    v-model="poi.name"
                    placeholder="请输入设施名称"
                    :readonly="readonly"
                    clearable
                    class="poi-name-input"
                />
                <el-input
                    v-model="poi.coordinate"
                    placeholder="请输入站点WGS84坐标系经纬度"
                    :readonly="readonly"
                    clearable
                    class="poi-coordinate-input"
                />
                <el-button
                    v-if="index > 0 && !readonly"
                    type="text"
                    @click="removePOI(index)"
                    class="delete-btn"
                >
                    <i class="el-icon-delete"></i>
                </el-button>
            </div>
        </div>
        <div v-if="!readonly" class="add-btn-wrapper">
            <el-button type="text" class="add-btn" @click="addPOI">
                <i class="el-icon-plus"></i> 添加设备
            </el-button>
        </div>
    </div>
</template>

<script>
export default {
    name: 'POIList',
    props: {
        value: {
            type: Array,
            required: true
        },
        readonly: {
            type: Boolean,
            default: false
        }
    },
    computed: {
        pois: {
            get() {
                return this.value;
            },
            set(value) {
                this.$emit('input', value);
            }
        }
    },
    methods: {
        addPOI() {
            const newPois = [...this.pois, { name: '', coordinate: '' }];
            this.$emit('input', newPois);
        },
        removePOI(index) {
            const newPois = [...this.pois];
            newPois.splice(index, 1);
            this.$emit('input', newPois);
        }
    }
};
</script>

<style lang="less" scoped>
.poi-content {
    width: 100%;
}
.poi-list {
    .poi-item {
        display: flex;
        gap: 14px;
        margin-bottom: 22px;
        position: relative;

        .poi-name-input {
            width: 180px;
        }

        .poi-coordinate-input {
            flex: 1;
        }

        .delete-btn {
            position: absolute;
            top: 50%;
            right: 0;
            transform: translate(100%, -50%);
            color: #ff4d4f;
            padding: 0 8px;
        }
    }
}

.add-btn-wrapper {
    background: rgba(0, 149, 255, 0.25);
    border-radius: 2px;
    border: 1px solid rgba(18, 139, 207, 0.6);
    height: 40px;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;

    &:hover {
        border-color: #0091ff;
    }

    .add-btn {
        color: #0091ff;
        height: 100%;
        width: 100%;
        font-weight: bold;
        display: flex;
        align-items: center;
        justify-content: center;

        .el-icon-plus {
            margin-right: 4px;
        }
    }
}
</style>
