<template>
    <div class="content mtex-general-display-board-dark-theme">
        <div class="search-container">
            <searchBar :fields="fields" :form="form">
                <div class="tools">
                    <el-button
                        type="primary"
                        size="mini"
                        @click="search(1)"
                        :style="{ visibility: showSearchButton ? 'visible' : 'hidden' }"
                        >查询</el-button
                    >
                    <el-button
                        v-if="addButtonText"
                        icon="el-icon-plus"
                        type="primary"
                        size="mini"
                        @click="add"
                        >{{ addButtonText }}</el-button
                    >
                </div>
            </searchBar>
        </div>
        <!-- 表格 -->
        <dataTable
            ref="listTable"
            class="list-table"
            :columns="columns"
            :data="tableData"
            :total="total"
            :layout="'total, prev, pager, next, sizes, jumper'"
            :pagination="pagination"
            :updateTable="getTableData"
            stripe
            isHideUpLine
        >
            <!-- 默认插槽 -->
            <template v-for="(_, name) in $slots" v-slot:[name]="slotData">
                <slot :name="name" v-bind="slotData"></slot>
            </template>

            <!-- 具名插槽 -->
            <template v-for="(_, name) in $scopedSlots" v-slot:[name]="slotData">
                <slot :name="name" v-bind="slotData"></slot>
            </template>

            <!-- 默认操作按钮 -->
            <template #operation="{ row }" v-if="!$scopedSlots.operation">
                <el-button class="p-0" type="text" @click="check(row)">详情</el-button>
                <el-button class="p-0" type="text" @click="edit(row)">修改</el-button>
                <el-button class="p-0" type="text" @click="del(row)">删除</el-button>
            </template>
        </dataTable>
    </div>
</template>

<script>
import searchBar from '../../components/searchBar/index.vue';
import dataTable from '@/script/components/dataTable/index.vue';

export default {
    name: 'common-table-content',
    components: {
        searchBar,
        dataTable
    },
    props: {
        // 表格数据
        data: {
            type: Array,
            default: () => []
        },
        // 表格数据总数
        dataTotal: {
            type: Number,
            default: 0
        },
        // 搜索表单配置项
        fields: {
            type: Array,
            required: true
        },
        // 表格列配置
        columns: {
            type: Array,
            required: true
        },
        // 初始搜索表单数据
        initialForm: {
            type: Object,
            default: () => ({})
        },
        // "新增"按钮文本，如果为空则不显示按钮
        addButtonText: {
            type: String,
            default: ''
        },
        // 是否开启删除功能
        enableDelete: {
            type: Boolean,
            default: true
        },
        // 是否显示搜索按钮
        showSearchButton: {
            type: Boolean,
            default: true
        }
    },
    data() {
        return {
            form: { ...this.initialForm },
            total: 0,
            pagination: {
                curPage: 1,
                pageSize: 15
            },
            tableData: []
        };
    },
    watch: {
        data: {
            handler(newData) {
                this.tableData = newData;
            },
            immediate: true
        },
        dataTotal: {
            handler(newData) {
                this.total = newData;
            },
            immediate: true
        }
    },
    methods: {
        search(pageIndex = 1) {
            this.pagination.curPage = pageIndex;
            this.$refs.listTable.scrollToTop();
            this.getTableData(this.pagination);
        },
        getTableData(paginationData = {}) {
            this.$emit(
                'get-table-data',
                {
                    ...this.form
                },
                paginationData
            );
        },
        add() {
            this.$emit('action', 'add', this.form);
        },
        edit(row) {
            this.$emit('action', 'edit', row);
        },
        check(row) {
            this.$emit('action', 'check', row);
        },
        del(row) {
            if (!this.enableDelete) return;
            this.$emit('delete', row);
        }
    }
};
</script>

<style lang="less" scoped>
@import '../../common.less';
.content {
    box-shadow: 0 2px 10px 0 rgba(0, 0, 0, 0.05);
    background: rgba(0, 42, 92, 0.4);
    border-radius: 4px;
    height: 100%;
    display: flex;
    flex-direction: column;
    padding: 18px 20px;

    .search-container {
        height: 44px;

        .tools {
            width: 100%;
            display: flex;
            justify-content: space-between;
        }
    }

    .list-table {
        flex: 1;
        height: 0;
    }
}
.p-0 {
    padding: 0;
}
/deep/ .el-button {
    &--text {
        color: #0095ff;
        &:hover {
            color: #fff;
        }
    }
}
</style>
