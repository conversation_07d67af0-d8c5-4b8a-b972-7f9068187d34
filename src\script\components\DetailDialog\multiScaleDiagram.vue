<template>
    <div class="content-container">
        <div
            class="content-mask"
            v-if="Object.keys(countData).length === 0 && chartsData.length === 0"
        >
            <div class="mask-content">
                <span>暂无数据</span>
            </div>
        </div>
        <div class="content-main" v-else>
            <!-- 顶部用户统计 -->
            <div class="user-stats">
                <img :src="personGroup" />
                <div class="gender-numbers">
                    <div v-for="(value, key) in countData" :key="key" class="gender-item">
                        <span class="gender-value">{{ value }}</span>
                        <span class="gender-label">{{ key }}</span>
                    </div>
                </div>
            </div>

            <!-- 图表区域 -->
            <div class="charts-grid">
                <div class="chart-item" v-for="(item, index) in chartsData" :key="index">
                    <div :id="'chart' + index" class="echarts-item"></div>
                    <div class="chart-info">
                        <span class="chart-total">{{ item.total }}</span>
                        <span class="chart-label">{{ item.label }}</span>
                    </div>
                </div>
            </div>
        </div>
    </div>
</template>

<script>
import * as echarts from 'echarts';
import personGroup from '../../../img/detail/personGroup.png';
export default {
    props: {
        resultData: {
            type: Array,
            default: () => []
        }
    },
    data() {
        return {
            personGroup,
            countData: {},
            chartsData: [],
            charts: [],
            isComponentMounted: false
        };
    },
    watch: {
        resultData: {
            handler(newVal) {
                if (!Array.isArray(newVal) || newVal.length === 0) {
                    this.countData = {};
                    this.chartsData = [];
                    return;
                }

                const { countData, chartsData } = this.transformData(newVal[0]);
                this.countData = countData;
                this.chartsData = chartsData;

                // 只有在组件挂载后才初始化图表
                if (this.isComponentMounted) {
                    this.$nextTick(() => {
                        this.drawChart();
                    });
                }
            },
            deep: true,
            immediate: true
        }
    },
    methods: {
        // 添加新方法处理数据
        handleData(data) {
            const { countData, chartsData } = this.transformData(data[0]);
            this.countData = countData;
            this.chartsData = chartsData;

            this.$nextTick(() => {
                this.drawChart();
            });

            // 处理完后清空待处理数据
            this.pendingData = null;
        },
        transformData(input) {
            const COUNT_PREFIX = 'countData-';
            const CHART_REGEX = /^chartsData-(percent|total)-(.+)$/;

            const countData = {};
            const chartMap = new Map();

            // 返回 a 与 b 的最长公共前缀长度
            function commonPrefixLen(a, b) {
                const minLen = Math.min(a.length, b.length);
                let i = 0;
                for (; i < minLen; i++) {
                    if (a[i] !== b[i]) break;
                }
                return i;
            }

            // 查找具有最长公共前缀的已存在 key
            function findBestKey(label) {
                let bestKey = null;
                let bestLen = 0;
                for (const existingKey of chartMap.keys()) {
                    const len = commonPrefixLen(existingKey, label);
                    if (len > bestLen) {
                        bestLen = len;
                        bestKey = existingKey;
                    }
                }
                if (bestKey === null || bestKey === undefined) {
                    return label;
                }
                return bestKey;
            }

            for (const [key, value] of Object.entries(input)) {
                if (key.startsWith(COUNT_PREFIX)) {
                    // 处理 countData-*
                    countData[key.slice(COUNT_PREFIX.length)] = value;
                } else if (CHART_REGEX.test(key)) {
                    const [, type, rawLabel] = key.match(CHART_REGEX);
                    const baseKey = findBestKey(rawLabel);

                    const obj = chartMap.get(baseKey) || {};
                    if (type === 'total') obj.label = rawLabel; // 保存完整 label

                    obj[type] = value;
                    chartMap.set(baseKey, obj);
                }
            }

            return {
                countData,
                chartsData: Array.from(chartMap.values())
            };
        },
        getOption(data) {
            return {
                title: [
                    {
                        text: data + '%',
                        textStyle: {
                            fontSize: 20,
                            color: '#4986FF',
                            fontWeight: 'bold'
                        },
                        subtextStyle: {
                            fontSize: 35,
                            color: '#C4C4C4'
                        },
                        x: 'center',
                        y: 'center'
                    }
                ],
                backgroundColor: 'transparent',
                tooltip: { show: false },
                legend: { show: false },
                polar: {
                    radius: ['65%', '82%'],
                    center: ['50%', '50%']
                },
                angleAxis: {
                    max: 100,
                    show: false,
                    startAngle: 90, //开始的角度
                    clockwise: false // 设置角度轴逆时针
                },
                radiusAxis: {
                    type: 'category',
                    show: true,
                    axisLabel: { show: false },
                    axisLine: { show: false },
                    axisTick: { show: false }
                },

                series: [
                    {
                        stack: 'round',
                        z: 11,
                        type: 'bar',
                        data: [0.01],
                        showBackground: false,
                        coordinateSystem: 'polar',
                        roundCap: true,
                        barWidth: 28,
                        itemStyle: {
                            color: '#fff',
                            borderColor: '#4986FF',
                            borderWidth: 4
                        }
                    },
                    {
                        stack: 'round',
                        name: '开挖',
                        type: 'bar',
                        silent: true,
                        // roundCap: true, //圆角
                        barWidth: 28, //大的占比环
                        showBackground: true,
                        backgroundStyle: {
                            color: '#C5D1E5'
                        },
                        coordinateSystem: 'polar',
                        itemStyle: {
                            color: {
                                type: 'linear',
                                x: 0,
                                y: 0,
                                x2: 0,
                                y2: 1,
                                colorStops: [
                                    {
                                        offset: 0,
                                        color: '#4987FF'
                                    },
                                    {
                                        offset: 1,
                                        color: '#44C0F7'
                                    }
                                ],
                                global: false
                            }
                        },
                        data: [data],
                        clockWise: true //顺时加载
                    }
                ]
            };
        },
        drawChart() {
            // 清除旧的图表实例
            this.charts.forEach((chart) => {
                chart.dispose();
            });
            this.charts = [];

            // 重新初始化图表
            this.chartsData.forEach((item, index) => {
                const dom = document.getElementById('chart' + index);
                if (!dom) {
                    return;
                }

                try {
                    if (dom.offsetHeight === 0 || dom.offsetWidth === 0) {
                        return;
                    }

                    const chart = echarts.init(dom);
                    const option = this.getOption(item.percent || 0);
                    chart.setOption(option);
                    this.charts.push(chart);

                    chart.resize();
                } catch (error) {
                    //
                }
            });
        },
        handleResize() {
            this.charts.forEach((chart) => {
                chart.resize();
            });
        }
    },
    mounted() {
        this.isComponentMounted = true;
        // 如果已经有数据，初始化图表
        if (this.chartsData.length > 0) {
            this.$nextTick(() => {
                this.drawChart();
            });
        }
        this.$nextTick(() => {
            // 添加窗口大小变化监听
            window.addEventListener('resize', this.handleResize);
        });
    },
    beforeDestroy() {
        // 清除事件监听和图表实例
        window.removeEventListener('resize', this.handleResize);
        this.charts.forEach((chart) => {
            chart.dispose();
        });
    }
};
</script>

<style lang="less" scoped>
.content-container {
    width: 100%;
    height: 100%;
    .content-mask {
        width: 100%;
        height: 100%;
        display: flex;
        align-items: center;
        justify-content: center;
    }
    .content-main {
        width: 100%;
        height: 100%;
    }

    .user-stats {
        display: flex;
        align-items: center;
        justify-content: center;
        margin-bottom: 2.22rem;
        img {
            width: 3.33rem;
            height: 3.39rem;
            margin-right: 2.78rem;
        }
        .gender-numbers {
            display: flex;
            gap: 2rem;
            color: #666;
            font-size: 0.78rem;
            .gender-item {
                display: flex;
                flex-direction: column;
                .gender-label {
                    font-size: 0.83rem;
                    color: #686e80;
                    margin-top: 0.78rem;
                }
                .gender-value {
                    font-size: 1.11rem;
                    color: #4986ff;
                    font-weight: bold;
                }
            }
        }
    }

    .charts-grid {
        display: grid;
        grid-template-columns: repeat(2, 1fr);
        justify-items: center;
        .chart-item {
            width: 100%;
            display: flex;
            align-items: center;
            justify-content: center;
            .echarts-item {
                width: 10rem;
                height: 10.44rem;
            }
            .chart-info {
                white-space: nowrap;

                .chart-total {
                    display: block;
                    font-size: 1.39rem;
                    color: #4986ff;
                    font-weight: bold;
                    margin: 4px 0;
                }

                .chart-label {
                    display: block;
                    font-size: 0.83rem;
                    color: #686e80;
                }
            }
            &:nth-child(1),
            &:nth-child(3) {
                border-right: 0.11rem solid;
                border-image: linear-gradient(
                        180deg,
                        rgba(72, 143, 254, 0) 0%,
                        rgba(72, 143, 254, 0) 25%,
                        #488ffe 50%,
                        #488ffe 65%,
                        rgba(72, 143, 254, 0) 85%
                    )
                    1;
            }
        }
    }
}
</style>
