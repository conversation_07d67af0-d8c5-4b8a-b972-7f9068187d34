<template>
    <commonTableContent
        ref="busRouteListTable"
        :fields="fields"
        :initialForm="initialForm"
        addButtonText=""
        :columns="columns"
        :data="tableData"
        :dataTotal="total"
        @get-table-data="handleGetTableData"
        @action="handleAction"
        @delete="handleDelete"
    >
    </commonTableContent>
</template>

<script>
import commonTableContent from '../../../components/commonTableContent/index.vue';
import {
    cellSiteFormCols,
    cellSiteTableCols
} from '@/script/constant/cityRoadNetwork/cellSiteRelationView.js';

export default {
    name: 'table-content',
    components: {
        commonTableContent
    },
    data() {
        return {
            initialForm: {
                cityId: '',
                subwayLine: undefined,
                coverageType: undefined,
                stationCategory: undefined
            },
            total: 0,
            tableData: []
        };
    },
    computed: {
        fields() {
            return cellSiteFormCols;
        },
        columns() {
            return cellSiteTableCols;
        }
    },
    methods: {
        async handleGetTableData(params, pagination) {
            // 暂时使用测试数据，等接口提供后替换
            // params 参数在真实接口中会用到，包含筛选条件
            try {
                // 模拟API延迟
                await new Promise((resolve) => setTimeout(resolve, 500));

                // 生成测试数据
                const mockData = this.generateMockData(pagination.pageSize);

                this.tableData = mockData;
                this.total = 50; // 模拟总数
            } catch (error) {
                console.error('获取基站小区数据失败:', error);
                this.tableData = [];
                this.total = 0;
            }

            // 真实接口调用代码（暂时注释）
            /*
            const payload = {
                cityId: params.cityId,
                subwayLine: params.subwayLine,
                coverageType: params.coverageType,
                stationCategory: params.stationCategory,
                pageNo: pagination.curPage,
                pageSize: pagination.pageSize
            };

            let { data } = await $request(
                'post',
                'mtexapi/region-service/management/cellsite/relation/query',
                payload
            );
            this.tableData = data.data || data.list || [];
            this.total = data.total || 0;
            */
        },
        handleAction(action, row = {}) {
            // 将action转发到父组件
            this.$emit('action', action, row);
        },
        handleDelete(row) {
            // 删除逻辑
            // row 参数在真实接口中会用到，包含要删除的记录信息
            this.$confirm('确定要删除该基站小区关系记录吗？', '提示', {
                confirmButtonText: '确定',
                cancelButtonText: '取消',
                type: 'warning'
            })
                .then(() => {
                    // 暂时使用模拟删除，等接口提供后替换
                    setTimeout(() => {
                        this.refreshTable();
                        this.$message({
                            type: 'success',
                            message: '删除成功'
                        });
                    }, 300);

                    // 真实接口调用代码（暂时注释）
                    /*
                    $request('post', 'mtexapi/region-service/management/cellsite/relation/delete', {
                        ids: [row.cellEci]
                    })
                        .then(({ serviceFlag, returnMsg }) => {
                            if (serviceFlag === 'TRUE') {
                                this.refreshTable();
                                this.$message({
                                    type: 'success',
                                    message: '删除成功'
                                });
                            } else {
                                this.$message({
                                    type: 'error',
                                    message: returnMsg
                                });
                            }
                        })
                        .catch(() => {});
                    */
                })
                .catch(() => {
                    // 用户取消删除
                });
        },
        generateMockData(pageSize) {
            const cities = ['济南市', '青岛市', '烟台市', '潍坊市', '临沂市'];
            const subwayLines = ['1号线', '2号线', '3号线', '4号线', '5号线'];
            const coverageTypes = ['室内', '室外'];
            const stationCategories = ['4G', '5G'];
            const stationNames = [
                '泉城广场站',
                '趵突泉站',
                '五四广场站',
                '栈桥站',
                '大明湖站',
                '千佛山站',
                '奥体中心站',
                '高新区站'
            ];

            const mockData = [];
            for (let i = 0; i < pageSize; i++) {
                const cityIndex = Math.floor(Math.random() * cities.length);
                const lineIndex = Math.floor(Math.random() * subwayLines.length);
                const stationIndex = Math.floor(Math.random() * stationNames.length);

                mockData.push({
                    id: `cell_${Date.now()}_${i}`,
                    cityName: cities[cityIndex],
                    cityId: `city_${cityIndex + 1}`,
                    subwayLineName: subwayLines[lineIndex],
                    subwayLine: `line${lineIndex + 1}`,
                    stationName: stationNames[stationIndex],
                    cellEci: `ECI${String(Math.floor(Math.random() * 999999)).padStart(6, '0')}`,
                    coverageType: coverageTypes[Math.floor(Math.random() * coverageTypes.length)],
                    stationCategory:
                        stationCategories[Math.floor(Math.random() * stationCategories.length)],
                    signalStrength: Math.floor(Math.random() * 40) + 60, // 60-100之间的信号强度
                    latitude: (36.6 + Math.random() * 0.2).toFixed(6), // 济南附近的纬度
                    longitude: (117.0 + Math.random() * 0.3).toFixed(6), // 济南附近的经度
                    lastUpdateTime: new Date(Date.now() - Math.random() * 30 * 24 * 60 * 60 * 1000)
                        .toISOString()
                        .slice(0, 19)
                        .replace('T', ' ')
                });
            }
            return mockData;
        },
        refreshTable() {
            this.$refs.busRouteListTable.search();
        }
    },
    mounted() {
        this.refreshTable();
    }
};
</script>
