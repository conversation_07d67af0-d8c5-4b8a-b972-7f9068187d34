<template>
    <div class="dashboard">
        <!-- 顶部用户统计 -->
        <div class="user-stats">
            <div class="user-icon">
                <div class="icon">👤</div>
                <div class="stats">
                    <div class="total">{{ totalUsers }}</div>
                    <div class="gender-numbers">
                        <span>男性群体 {{ genderData.male }}</span>
                        <span>女性群体 {{ genderData.female }}</span>
                    </div>
                </div>
            </div>
        </div>

        <!-- 图表网格 -->
        <div class="charts-grid">
            <div class="chart-item" v-for="(item, index) in ageCharts" :key="index">
                <div class="chart-container">
                    <v-chart class="chart" :option="getChartOption(item.percentage)" />
                    <div class="chart-info">
                        <span class="percentage">{{ item.percentage }}%</span>
                        <span class="total">{{ item.total }}</span>
                        <span class="label">{{ item.label }}</span>
                    </div>
                </div>
            </div>
        </div>
    </div>
</template>

<script setup lang="ts">
import { computed } from 'vue';
import VChart, { THEME_KEY } from 'vue-echarts';
import { use } from 'echarts/core';
import { PieChart } from 'echarts/charts';
import { TitleComponent, TooltipComponent, LegendComponent } from 'echarts/components';
import { CanvasRenderer } from 'echarts/renderers';

// 注册 ECharts 组件
use([CanvasRenderer, PieChart, TitleComponent, TooltipComponent, LegendComponent]);

// 数据
const genderData = {
    male: 1800,
    female: 1200
};

const totalUsers = computed(() => genderData.male + genderData.female);

const ageCharts = [
    {
        percentage: 60.3,
        total: 18652,
        label: '18岁以下人数'
    },
    {
        percentage: 60.3,
        total: 18652,
        label: '18岁以下人数'
    },
    {
        percentage: 60.3,
        total: 18652,
        label: '18岁以下人数'
    },
    {
        percentage: 60.3,
        total: 18652,
        label: '18岁以下人数'
    }
];

const getChartOption = (percentage: number) => ({
    series: [
        {
            type: 'pie',
            radius: ['75%', '90%'],
            avoidLabelOverlap: false,
            label: {
                show: false
            },
            emphasis: {
                disabled: true
            },
            data: [
                {
                    value: percentage,
                    name: '占比',
                    itemStyle: {
                        color: '#4B8BF4'
                    }
                },
                {
                    value: 100 - percentage,
                    name: '剩余',
                    itemStyle: {
                        color: '#E8E8E8'
                    }
                }
            ]
        }
    ]
});
</script>

<style scoped>
.dashboard {
    padding: 24px;
}

.user-stats {
    margin-bottom: 40px;
}

.user-icon {
    display: flex;
    align-items: center;
    gap: 16px;
}

.icon {
    font-size: 24px;
    color: #4b8bf4;
}

.stats .total {
    font-size: 20px;
    font-weight: bold;
    color: #333;
    margin-bottom: 4px;
}

.gender-numbers {
    display: flex;
    gap: 16px;
    color: #666;
    font-size: 14px;
}

.charts-grid {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 24px;
}

.chart-item {
    display: flex;
    justify-content: center;
}

.chart-container {
    position: relative;
    width: 200px;
    height: 200px;
}

.chart {
    height: 100%;
    width: 100%;
}

.chart-info {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    text-align: center;
    white-space: nowrap;
}

.percentage {
    display: block;
    font-size: 24px;
    font-weight: bold;
    color: #333;
}

.total {
    display: block;
    font-size: 20px;
    color: #4b8bf4;
    margin: 4px 0;
}

.label {
    display: block;
    font-size: 14px;
    color: #666;
}
</style>
