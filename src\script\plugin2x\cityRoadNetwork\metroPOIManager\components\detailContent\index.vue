<template>
    <div class="content">
        <Card title="站点基础属性">
            <div class="form-wrapper">
                <el-form
                    ref="form"
                    :model="formData"
                    :rules="rules"
                    label-width="180px"
                    class="form-container"
                >
                    <!-- 站点基础信息 -->
                    <div class="gdb-section-title">站点标识信息</div>
                    <el-form-item label="站点名称：" prop="stationName">
                        <el-input
                            v-model="formData.stationName"
                            placeholder="请输入应用名称"
                            :readonly="action === 'check'"
                            clearable
                        />
                    </el-form-item>
                    <el-form-item label="站点分类：" prop="stationType">
                        <template v-if="action === 'check'">
                            <el-input v-model="getStationTypeName" readonly />
                        </template>
                        <template v-else>
                            <el-select
                                v-model="formData.stationType"
                                placeholder="请选择站点分类"
                                clearable
                                popper-class="gdb-select-dropdown-dark gdb-popover-dark"
                            >
                                <el-option
                                    v-for="item in stationTypes"
                                    :key="item.value"
                                    :label="item.label"
                                    :value="item.value"
                                />
                            </el-select>
                        </template>
                    </el-form-item>
                    <el-form-item label="站点标签：" prop="stationTags">
                        <InputToTag v-model="formData.stationTags" :readonly="action === 'check'" />
                    </el-form-item>

                    <!-- 站点POI坐标配置 -->
                    <div class="gdb-section-title">站点POI坐标配置</div>
                    <el-form-item label="地铁站点主POI坐标：" prop="metroPOI">
                        <el-input
                            v-model="formData.metroPOI"
                            placeholder="请输入站点WGS84坐标系经纬度"
                            :readonly="action === 'check'"
                            clearable
                        />
                    </el-form-item>
                    <el-form-item label="归属地铁线路：" prop="belongLine">
                        <template v-if="action === 'check'">
                            <el-input v-model="getBelongLineName" readonly />
                        </template>
                        <template v-else>
                            <el-select
                                v-model="formData.belongLine"
                                placeholder="请选择归属地铁线路"
                                clearable
                                popper-class="gdb-select-dropdown-dark gdb-popover-dark"
                            >
                                <el-option
                                    v-for="item in metroLines"
                                    :key="item.value"
                                    :label="item.label"
                                    :value="item.value"
                                />
                            </el-select>
                        </template>
                    </el-form-item>
                    <el-form-item
                        label="地铁设施子POI坐标："
                        prop="entrancePOIs"
                        v-if="action !== 'check'"
                    >
                        <POIList v-model="formData.entrancePOIs" />
                    </el-form-item>
                    <template v-else>
                        <el-form-item
                            v-for="(item, index) in formData.entrancePOIs"
                            :key="index"
                            :label="`地铁${index === 0 ? '站点主' : '设施子'}POI坐标${index + 1}：`"
                            prop="entrancePOIs"
                        >
                            <div class="poi-item">
                                <el-input v-model="item.name" readonly class="left-input" />
                                <el-input v-model="item.coordinate" readonly class="right-input" />
                            </div>
                        </el-form-item>
                    </template>

                    <div class="form-footer" v-if="action !== 'check'">
                        <el-button class="form-footer-btn" type="primary" @click="submitForm"
                            >完成{{ modeText }}</el-button
                        >
                    </div>
                </el-form>
            </div>
        </Card>
    </div>
</template>

<script>
import Card from '../../../components/Card/index.vue';
import InputToTag from '@/script/components/InputToTag/index.vue';
import POIList from './POIList.vue';
import { validateWGS84 } from '@/script/utils/method.js';
import { stationTypes, metroLines } from '@/script/constant/cityRoadNetwork/metroPOIManager.js';

export default {
    name: 'add-or-edit-content',
    components: {
        Card,
        InputToTag,
        POIList
    },
    props: {
        row: {
            type: Object,
            default: () => ({})
        },
        action: {
            type: String,
            default: 'check'
        }
    },
    data() {
        return {
            formData: {
                stationName: '',
                stationType: '',
                stationTags: [],
                metroPOI: '',
                belongLine: '',
                entrancePOIs: [{ name: '', coordinate: '' }]
            },
            rules: {
                stationName: [{ required: true, message: '请输入应用名称', trigger: 'blur' }],
                stationType: [{ required: true, message: '请选择站点分类', trigger: 'change' }],
                stationTags: [{ required: true, message: '请输入站点标签', trigger: 'blur' }],
                metroPOI: [
                    { required: true, message: '请输入地铁站点POI坐标', trigger: 'blur' },
                    {
                        validator: (rule, value, callback) => {
                            if (!value) {
                                callback();
                                return;
                            }
                            if (!validateWGS84(value)) {
                                callback(
                                    new Error(
                                        '请输入正确的WGS84坐标格式(经度,纬度)，经度范围-180~180，纬度范围-90~90'
                                    )
                                );
                                return;
                            }
                            callback();
                        },
                        trigger: 'blur'
                    }
                ],
                belongLine: [{ required: true, message: '请选择地铁站点', trigger: 'change' }]
            }
        };
    },
    computed: {
        modeText() {
            const map = new Map([
                ['add', '新增'],
                ['edit', '修改'],
                ['check', '查看']
            ]);
            return map.get(this.action);
        },
        stationTypes() {
            return stationTypes;
        },
        metroLines() {
            return metroLines;
        },
        getStationTypeName() {
            const type = this.stationTypes.find((item) => item.value === this.formData.stationType);
            if (type) {
                return type.label;
            }
            return this.formData.stationType;
        },
        getBelongLineName() {
            const line = this.metroLines.find((item) => item.value === this.formData.belongLine);
            if (line) {
                return line.label;
            }
            return this.formData.belongLine;
        }
    },
    methods: {
        submitForm() {
            this.$refs.form.validate((valid) => {
                if (valid) {
                    // 过滤空数组
                    this.formData.entrancePOIs = this.formData.entrancePOIs.filter(
                        (item) => item.name || item.coordinate
                    );
                    const isEdit = this.action === 'edit';
                    $request(
                        'post',
                        `mtexapi/region-service/management/stations/${isEdit ? 'update' : 'add'}`,
                        {
                            id: this.formData.id || undefined,
                            stationName: this.formData.stationName,
                            stationType: this.formData.stationType,
                            belongLine: this.formData.belongLine,
                            stationLabel: this.formData.stationTags.join(','),
                            longitude: +this.formData.metroPOI.split(',')[0],
                            latitude: +this.formData.metroPOI.split(',')[1]
                        }
                    ).then(({ serviceFlag, returnMsg }) => {
                        if (serviceFlag === 'TRUE') {
                            this.$emit('goBack');
                            this.$message({
                                type: 'success',
                                message: `${isEdit ? '修改' : '新增'}成功`
                            });
                            return;
                        }
                        this.$message({
                            type: 'error',
                            message: returnMsg
                        });
                    });
                }
            });
        }
    },
    created() {
        if (['edit', 'check'].includes(this.action) && this.row) {
            this.formData = {
                ...this.formData,
                ...this.row,
                stationTags: []
            };
            if (this.row.stationTags.length > 0) {
                this.formData.stationTags = this.row.stationTags.split(',');
            }
        }
        // 查看不做校验
        if (['check'].includes(this.action)) {
            this.rules = {};
        }
    }
};
</script>

<style lang="less" scoped>
.content {
    min-height: 100%;
    height: max-content;
    display: flex;
    flex-direction: column;

    .form-wrapper {
        display: flex;
        justify-content: center;
        align-items: center;

        .form-container {
            width: 60%;
            height: 100%;

            .poi-item {
                display: flex;
                gap: 14px;
                position: relative;

                .left-input {
                    width: 180px;
                }

                .right-input {
                    flex: 1;
                }
            }

            .form-footer {
                padding-bottom: 22px;

                .form-footer-btn {
                    margin-left: 180px;
                }
            }
        }
    }
}
.el-select {
    width: 100%;
}
</style>
