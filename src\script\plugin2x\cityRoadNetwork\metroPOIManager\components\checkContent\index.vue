<template>
    <div class="check-content">
        <detailContent :row="row" action="check" />
        <Card title="站点可视化展示">
            <section class="section-wrapper">
                <div class="section-container">
                    <div class="gdb-section-title">站点访问统计</div>
                    <div class="gis-content">
                        <mtv-gis
                            class="home-map"
                            ref="mtvGis"
                            :totaloptions="gistotalOptions"
                            @onLoad="gisOnLoad"
                            :autoActive="false"
                        ></mtv-gis>
                    </div>
                </div>
            </section>
            <section class="section-wrapper">
                <div class="section-container">
                    <div class="gdb-section-title">站点访问统计</div>
                    <div class="chart-content">
                        <div class="chart-header">
                            <label>选择日期：</label>
                            <el-date-picker
                                v-model="chartDate"
                                type="date"
                                placeholder="选择日期"
                                format="yyyy-MM-dd"
                                value-format="yyyy-MM-dd"
                                class="chart-date-picker"
                                popper-class="gdb-date-picker-dark gdb-popover-dark"
                                @change="handleDateChange"
                            ></el-date-picker>
                        </div>
                        <div ref="lineChart" class="line-chart"></div>
                    </div>
                </div>
            </section>
        </Card>
    </div>
</template>

<script>
import Card from '../../../components/Card/index.vue';
import detailContent from '../detailContent/index.vue';
import { gistotalOptions, getMayType, changeGisColor } from '@/script/constant/gis.js';
import * as echarts from 'echarts';
import testimg from '@/img/common/positionMark.png';

export default {
    name: 'check-content',
    components: {
        Card,
        detailContent
    },
    props: {
        row: {
            type: Object,
            default: () => ({})
        }
    },
    data() {
        return {
            gistotalOptions,
            chart: null,
            chartData: {
                xData: [],
                yData: []
            },
            // 默认昨天
            chartDate: new Date(new Date().setDate(new Date().getDate() - 1))
                .toISOString()
                .split('T')[0]
        };
    },
    methods: {
        gisOnLoad() {
            this.dialogGIS = this.$refs.mtvGis.getEntity();
            // 设置底图
            if (getMayType() === 'default') {
                this.dialogGIS.tileLayerList['底图图层'].visible = false;
                //重写getUrl方法
                this.dialogGIS.tileLayerList['高德底图'].getUrl = (x, y, z) => {
                    return `${location.origin}/MapUrl/api/appmaptile?x=${x}&y=${y}&z=${z}`;
                };
            } else {
                this.dialogGIS.tileLayerList['高德底图'] &&
                    (this.dialogGIS.tileLayerList['高德底图'].visible = false);
            }
            changeGisColor(this.dialogGIS);

            //创建图层
            let layer = new this.dialogGIS.layer();
            layer.name = 'imgTest';
            this.dialogGIS.gis.scene.add(layer.Group);
            layer.visible = true;
            //传入图片url获取材质
            let material = this.dialogGIS.meshList.img.getMaterial({
                url: testimg,
                opacity: 0.8
            });
            //图片1
            let data = [
                {
                    lng: 116.38762954,
                    lat: 39.90381813,
                    ht: 0.2,
                    width: 64,
                    color: 0x33ff33
                },
                {
                    lng: 116.38753954,
                    lat: 39.90221813,
                    ht: 0.1,
                    width: 120,
                    color: 0xff0000
                },
                {
                    lng: 116.38463754,
                    lat: 39.90481813,
                    ht: 0.4,
                    width: 30,
                    color: 0xffff00
                },
                {
                    lng: 116.38863954,
                    lat: 39.90381313,
                    ht: 0.3,
                    width: 55,
                    color: 0x0000ff
                }
            ];
            data.autoScale = false;
            //生成模型
            let imgMesh = this.dialogGIS.meshList.img.create(data, material);
            //模型添加进图层
            layer.add(imgMesh);
            //更新GIS
            this.dialogGIS.gis.needUpdate = true;
            // hover监听
            this.dialogGIS.event.addHover(
                layer,
                (thData, event) => {
                    //当鼠标选中目标时触发,因为用的img模型,所以对应的执行img模型构造器的hover方法,并获取目标数据序号.
                    let index = this.dialogGIS.meshList.img.hover(thData, event);
                    //通过序号获取数据
                    let targetData = data[index];
                    //组织信息展示模块需要的数据格式
                    let showData = [
                        {
                            name: '栅格数据',
                            values: {
                                '宽度:': targetData.width,
                                '经度:': targetData.lng,
                                '纬度:': targetData.lat,
                                '高度:': targetData.ht,
                                '颜色:': targetData.color
                            },
                            checked: true
                        }
                    ];
                    this.dialogGIS.show.showInfo(showData, event);
                },
                () => {
                    //清除高亮
                    this.dialogGIS.meshList.img.clearHover();
                    //清除信息展示
                    this.dialogGIS.show.clearInfo();
                }
            );
        },

        // 生成随机颜色
        getRandomColor() {
            // 预定义一些好看的颜色
            const colors = [
                '#0B72FF',
                '#F36EB5',
                '#4B8BF4', // 蓝色
                '#FF6E76', // 红色
                '#19D1A3', // 绿色
                '#9287E7', // 紫色
                '#FFB72C', // 黄色
                '#5470C6', // 深蓝
                '#91CC75', // 浅绿
                '#FAC858', // 橙色
                '#EE6666', // 粉红
                '#73C0DE' // 天蓝
            ];
            return colors;
        },
        resetChartData() {
            this.chartData = {
                xData: [],
                yData: {}
            };
        },
        getChartData(data) {
            this.resetChartData();
            // 如果数据为空，清空图表
            if (!data || data.length === 0) {
                if (this.chart) {
                    this.chart.clear();
                }
                return;
            }
            let charObj = {};
            let time = [];
            let chartData = this.dealChartData(data);
            chartData.forEach((item) => {
                time.push(item.time);
                if (!charObj.hasOwnProperty(item.type)) {
                    charObj[item.type] = [];
                }
                charObj[item.type].push(item.value);
            });
            this.chartData.xData = [...new Set(time)];
            this.chartData.yData = charObj;
            this.initChart();
        },
        initChart() {
            // 销毁已有的图表实例
            if (this.chart) {
                this.chart.dispose();
                this.chart = null;
            }

            this.chart = echarts.init(this.$refs.lineChart);
            let seriesData = [];
            const colors = this.getRandomColor();
            let i = 0;

            // 准备图例数据
            const legendData = Object.keys(this.chartData.yData);

            for (let k in this.chartData.yData) {
                // 使用固定颜色，确保颜色一致性
                const color = colors[i % colors.length];
                seriesData.push({
                    name: k,
                    data: this.chartData.yData[k],
                    type: 'line',
                    showSymbol: false,
                    itemStyle: {
                        color: color // 设置图例和线条颜色一致
                    },
                    lineStyle: {
                        color: color,
                        width: 2
                    },
                    emphasis: {
                        scale: true,
                        focus: 'series',
                        itemStyle: {
                            color: color
                        }
                    },
                    areaStyle: {
                        color: {
                            type: 'linear',
                            x: 0,
                            y: 0,
                            x2: 0,
                            y2: 1,
                            colorStops: [
                                {
                                    offset: 0,
                                    color: `${color}6A` // 添加透明度
                                },
                                {
                                    offset: 1,
                                    color: `${color}0D` // 更浅的透明度
                                }
                            ]
                        }
                    }
                });
                i++;
            }
            const option = {
                color: colors, // 设置全局颜色
                grid: {
                    top: '12%',
                    left: '3%',
                    right: '3%',
                    bottom: '5%',
                    containLabel: true
                },
                tooltip: {
                    trigger: 'axis',
                    backgroundColor: '#013B70', // 设置为半透明黑色
                    borderColor: '#47C0FF', // 边框颜色
                    borderWidth: 1, // 边框宽度
                    textStyle: {
                        color: '#fff', // 文字颜色
                        fontSize: 14
                    },
                    axisPointer: {
                        type: 'line',
                        lineStyle: {
                            color: '#4B8BF4',
                            type: 'dashed'
                        }
                    },
                    formatter: (params) => {
                        let str = `<div style="margin-bottom: 10px;background: #013B70;font-family: Source Han Sans CN, Source Han Sans CN;">${params[0].name}</div>`;
                        params.forEach((item) => {
                            // 使用对应系列的颜色
                            str += `
                        <div>
                            <span style="display: inline-block; margin-right: 5px;">
                                <span style="display:inline-block;width:15px;height:2px;background-color:${item.color};margin-right:5px;vertical-align:middle;"></span>
                                ${item.seriesName}：
                            </span>
                            <span style="color: #13F3FB; font-weight: 500;font-family: Source Han Sans CN, Source Han Sans CN;">${item.value}</span>
                        </div>`;
                        });
                        return `<div>${str}</div>`;
                    }
                },
                legend: {
                    data: legendData,
                    top: '3%',
                    left: '3%',
                    icon: 'path://M100 0 H200 v10 H100 Z',
                    textStyle: {
                        color: '#fff'
                    }
                },
                xAxis: {
                    type: 'category',
                    data: this.chartData.xData,
                    axisLine: {
                        show: true,
                        lineStyle: {
                            color: '#77AAEA'
                        }
                    },
                    axisTick: {
                        show: false
                    },
                    axisLabel: {
                        color: '#92B9E5'
                    },
                    splitLine: {
                        show: true,
                        lineStyle: {
                            color: '#77AAEA',
                            type: 'dashed'
                        }
                    }
                },
                yAxis: {
                    type: 'value',
                    name: '人次',
                    nameTextStyle: {
                        color: '#92B9E5'
                    },
                    axisLine: {
                        show: false
                    },
                    axisTick: {
                        show: false
                    },
                    axisLabel: {
                        color: '#92B9E5'
                    },
                    splitLine: {
                        lineStyle: {
                            color: '#77AAEA',
                            type: 'dashed'
                        }
                    }
                },
                series: seriesData
            };
            this.chart.setOption(option);
        },
        dealChartData(data) {
            let chartData = data.reduce((result, item) => {
                // 添加流入人数数据
                result.push({
                    time: item.key,
                    type: '流入人数',
                    value: item.value
                });

                // 添加流出人数数据
                result.push({
                    time: item.key,
                    type: '流出人数',
                    value: Math.floor(item.value * 0.8) // 这里用0.8倍作为流出人数的模拟数据
                });

                return result;
            }, []);

            return chartData;
        },
        handleDateChange() {
            console.log(this.chartDate);
            this.getChartData(this.generateTestData());

            // 窗口大小变化时重新调整图表大小
            window.addEventListener('resize', this.resizeChart);
        },
        resizeChart() {
            if (this.chart) {
                this.chart.resize();
            }
        },
        generateTestData() {
            let data = [];
            for (let i = 0; i < 24; i++) {
                data.push({
                    key: `${i}点`,
                    value: 100 + Math.floor(Math.random() * 200)
                });
            }
            return data;
        }
    },
    mounted() {
        this.$nextTick(() => {
            console.log(this.chartDate);
            this.getChartData(this.generateTestData());

            // 窗口大小变化时重新调整图表大小
            window.addEventListener('resize', this.resizeChart);
        });
    },
    beforeDestroy() {
        // 组件销毁前清理事件监听和图表实例
        window.removeEventListener('resize', this.resizeChart);
        if (this.chart) {
            this.chart.dispose();
            this.chart = null;
        }
    }
};
</script>

<style lang="less" scoped>
.check-content {
    min-height: 100%;
    height: max-content;
    display: flex;
    flex-direction: column;
    gap: 6px;

    .section-wrapper {
        display: flex;
        justify-content: center;
        align-items: center;

        .section-container {
            width: 60%;
            height: 100%;

            .gis-content {
                position: relative;
                width: 100%;
                height: 0;
                padding: 0;
                padding-bottom: 56.25%;

                .home-map {
                    border-radius: 2px;
                    border: 1px solid rgba(64, 155, 255, 0.3);
                    position: absolute;
                    top: 0;
                    right: 0;
                    bottom: 0;
                    left: 0;
                }
            }

            .chart-content {
                position: relative;
                width: 100%;
                height: 0;
                padding: 24px 0 55%;
                background: rgba(0, 50, 102, 0.4);
                border: 1px solid rgba(64, 155, 255, 0.3);

                .chart-header {
                    font-size: 12px;
                    font-weight: bold;
                    color: rgba(255, 255, 255, 0.65);
                    line-height: 20px;
                    position: absolute;
                    top: 12px;
                    right: 3%;
                    z-index: 1000;

                    .el-date-editor {
                        width: 150px;
                    }
                    /deep/.el-input__inner {
                        height: 32px;
                        line-height: 32px;
                        font-size: 12px;
                        border-radius: 0;
                        padding-left: 30px;
                    }
                    /deep/.el-input__icon {
                        line-height: 32px;
                    }
                }

                .line-chart {
                    position: absolute;
                    top: 0;
                    right: 0;
                    bottom: 0;
                    left: 0;
                }
            }
        }
    }
}
</style>
