<template>
    <el-time-picker
        :style="[$attrs.itemStyle || {}]"
        v-model="currentVal"
        placeholder="请选择时间"
        :class="addClass"
        is-range
        range-separator="至"
        start-placeholder="开始时间"
        end-placeholder="结束时间"
        format="HH:00"
        value-format="HH:00"
        :picker-options="{
            format: 'HH:00',
            hours: hourList,
            minutes: [0],
            seconds: [0]
        }"
        v-bind="$attrs"
        v-on="$listeners"
        @focus="hideMinuteColumns"
        @change="changeInput"
        :popper-append-to-body="false"
        popper-class="diyTimePanel"
        class="form-sdate"
    >
    </el-time-picker>
</template>

<script>
export default {
    name: 'RamsSingleTime',
    model: {
        prop: 'value',
        event: 'change'
    },
    props: {
        value: {
            type: [String, Array],
            default: () => []
        },
        addClass: {
            type: [Object, Array, String]
        }
    },
    data() {
        return {
            currentVal: this.initCurrentVal(),
            hourList: Array.from({ length: 25 }, (_, i) => i)
        };
    },

    watch: {
        value(val) {
            if (Array.isArray(val)) {
                this.currentVal = val;
                return;
            }
            if (val) {
                this.currentVal = val.split(',');
                return;
            }
            this.currentVal = ['00:00', '00:00'];
        }
    },

    methods: {
        initCurrentVal() {
            if (Array.isArray(this.value)) {
                return this.value;
            }
            if (this.value) {
                return this.value.split(',');
            }
            return ['00:00', '00:00'];
        },
        hideMinuteColumns() {
            // 使用定时器确保在面板完全展开后执行
            setTimeout(() => {
                const timePanels = document.querySelectorAll('.el-time-range-picker__content');
                timePanels.forEach((panel) => {
                    const spinners = panel.querySelectorAll('.el-time-spinner');
                    spinners.forEach((spinner) => {
                        const columns = spinner.children;
                        for (let i = 1; i < columns.length; i++) {
                            columns[i].style.display = 'none';
                        }
                    });
                });
            }, 0);
        },
        changeInput(newVal) {
            if (newVal) {
                // 确保时间格式正确，只保留小时
                const [start, end] = newVal;
                this.currentVal = [start.substring(0, 2) + ':00', end.substring(0, 2) + ':00'];
            }
            let currentVal = this.currentVal.join();
            this.$emit('change', currentVal);
        }
    }
};
</script>

<style lang="less" scoped>
.form-sdate {
    /deep/.el-input__inner {
        background: #013b70;
        border: 1px solid #47c0ff;
        color: #91aac1;
    }
}
/deep/ .diyTimePanel {
    background: #013b70;
    box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
    border-radius: 6px;
    border: 1px solid #47c0ff;
    color: #c9dfff;

    .el-time-panel__content {
        background: #013b70;
    }
    .el-time-spinner__item {
        color: #fff;
        font-size: 16px;
        &:hover,
        &.active {
            background: #128bcf;
            color: #00f5f2;
        }
    }
    .el-time-panel__footer {
        background: #013b70;
        border-top: 1px solid #47c0ff;
    }
    .el-time-panel__btn {
        color: #47c0ff;
    }
}
</style>
