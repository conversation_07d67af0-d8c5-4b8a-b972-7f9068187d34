<template>
    <div class="content-container">
        <!-- 圆环图 -->
        <div class="ring-chart" ref="ringChart"></div>
        <!-- 进度条列表 -->
        <div class="progress-list">
            <div class="progress-item" v-for="(item, index) in progressData" :key="index">
                <span class="label">{{ item.label }}</span>
                <div class="progress-bar-item">
                    <div
                        class="progress"
                        :style="{
                            width: item.percentage + '%',
                            backgroundColor: item.color
                        }"
                    ></div>
                </div>
                <span class="percentage">{{ item.percentage }}%</span>
            </div>
        </div>
    </div>
</template>

<script>
import * as echarts from 'echarts';

export default {
    props: {
        resultData: {
            type: Array,
            default: () => [
                {
                    totalNum: 0,
                    progressData: []
                }
            ]
        }
    },
    data() {
        return {
            chart: null,
            totalNum: 0,
            progressData: []
        };
    },
    watch: {
        resultData: {
            handler(newVal) {
                // newVal = [
                //     {
                //         totalNum: 10298,
                //         progressData: [
                //             { label: '0.5h以下', percentage: 34.21, color: '#FF6E76' },
                //             { label: '0.5h-1h', percentage: 35.56, color: '#4B8BF4' },
                //             { label: '1h-2h', percentage: 34.21, color: '#19D1A3' },
                //             { label: '2h以上', percentage: 1, color: '#9287E7' }
                //         ]
                //     }
                // ];
                if (!Array.isArray(newVal) || newVal.length === 0) {
                    this.progressData = [
                        { label: '0.5h以下', percentage: '-', color: '#FF6E76' },
                        { label: '0.5h-1h', percentage: '-', color: '#4B8BF4' },
                        { label: '1h-2h', percentage: '-', color: '#19D1A3' },
                        { label: '2h以上', percentage: '-', color: '#9287E7' }
                    ];
                    this.totalNum = '-';
                } else {
                    this.totalNum = newVal[0].totalNum;
                    this.progressData = newVal[0].progressData;
                }

                this.$nextTick(() => {
                    this.initChart();
                });
            },
            deep: true,
            immediate: true
        }
    },
    methods: {
        initChart() {
            if (!this.$refs.ringChart) return;

            if (this.chart) {
                this.chart.dispose();
            }

            this.chart = echarts.init(this.$refs.ringChart);
            const option = {
                title: [
                    {
                        text: this.totalNum,
                        subtext: '驻留总人群',
                        textStyle: {
                            fontSize: 24,
                            color: '#fff',
                            fontWeight: 'bold'
                        },
                        subtextStyle: {
                            fontSize: 15,
                            color: '#92b9e5'
                        },
                        x: 'center',
                        y: 'center',
                        itemGap: 1
                    }
                ],
                backgroundColor: 'transparent',
                tooltip: { show: false },
                legend: { show: false },
                polar: {
                    radius: ['65%', '76%'],
                    center: ['50%', '50%']
                },
                angleAxis: {
                    max: 100,
                    show: false,
                    startAngle: 90,
                    clockwise: false
                },
                radiusAxis: {
                    type: 'category',
                    show: true,
                    axisLabel: { show: false },
                    axisLine: { show: false },
                    axisTick: { show: false }
                },
                series: [
                    {
                        stack: 'round',
                        z: 11,
                        type: 'bar',
                        data: [0.01],
                        showBackground: false,
                        coordinateSystem: 'polar',
                        roundCap: true,
                        barWidth: 40,
                        itemStyle: {
                            color: '#fff',
                            borderColor: '#4986FF',
                            borderWidth: 4
                        }
                    },
                    {
                        stack: 'round',
                        name: '开挖',
                        type: 'bar',
                        silent: true,
                        barWidth: 40,
                        showBackground: true,
                        backgroundStyle: {
                            color: '#EEF4F4'
                        },
                        coordinateSystem: 'polar',
                        data: [
                            {
                                value: 60,
                                name: '进度',
                                itemStyle: {
                                    color: '#187DEC',
                                    borderRadius: 10
                                }
                            }
                        ]
                    }
                ]
            };
            this.chart.setOption(option);
        },
        handleResize() {
            if (this.chart) {
                this.chart.resize();
            }
        }
    },
    mounted() {
        window.addEventListener('resize', this.handleResize);
    },
    beforeDestroy() {
        window.removeEventListener('resize', this.handleResize);
        if (this.chart) {
            this.chart.dispose();
            this.chart = null;
        }
    }
};
</script>

<style lang="less" scoped>
.content-container {
    width: 100%;
    height: 100%;
    padding: 1.11rem 5rem;

    .ring-chart {
        width: 200px;
        height: 200px;
        margin: 0 auto 30px;
    }

    .progress-list {
        .progress-item {
            display: flex;
            align-items: center;
            margin-bottom: 2.22rem;

            .label {
                width: 4.44rem;
                text-align: left;
                color: #92b9e5;
                font-size: 14px;
                font-weight: normal;
            }

            .progress-bar-item {
                flex: 1;
                height: 0.28rem;
                background-color: #eef4f4;
                margin: 0 2.22rem;
                border-radius: 2px;
                overflow: hidden;

                .progress {
                    height: 100%;
                    transition: width 0.3s ease;
                }
            }

            .percentage {
                min-width: 3.33rem;
                color: #92b9e5;
                font-size: 0.83rem;
                font-weight: bold;
            }
        }
    }
}
</style>
