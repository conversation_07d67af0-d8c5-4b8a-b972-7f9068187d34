<template>
    <el-button
        v-bind="$attrs"
        v-on="$listeners"
        :loading="loading"
        :class="[addClass, loading ? 'activeBtn' : '']"
        :style="itemStyle"
        @click="click"
    >
        {{ btnName }}
    </el-button>
</template>

<script>
export default {
    name: '<PERSON><PERSON>utt<PERSON>',
    props: {
        btnName: {
            type: String,
            default: '按钮'
        },
        itemStyle: {
            type: Object
        },
        addClass: {
            type: [Object, Array, String]
        },
        /*
            开启loading后 需要调用者 自行获取按钮实例关闭loading属性
        */
        needLoading: {
            type: Boolean,
            default: false
        }
    },
    data() {
        return {
            loading: false
        };
    },

    mounted() {},

    methods: {
        click() {
            if (this.needLoading) {
                this.loading = true;
            }
        }
    }
};
</script>
<style lang="less" scoped></style>
