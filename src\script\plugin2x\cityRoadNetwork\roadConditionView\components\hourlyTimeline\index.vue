<template>
    <div class="hourly-timeline">
        <div class="timeline-header">
            <span class="timeline-title">时间轴</span>
            <div class="timeline-controls">
                <el-button
                    type="text"
                    size="mini"
                    @click="toggleAutoPlay"
                    :class="{ active: isAutoPlaying }"
                >
                    <i :class="isAutoPlaying ? 'el-icon-video-pause' : 'el-icon-video-play'"></i>
                    {{ isAutoPlaying ? '暂停' : '播放' }}
                </el-button>
            </div>
        </div>
        <div class="timeline-container">
            <div class="timeline-track">
                <div
                    v-for="hour in hours"
                    :key="hour"
                    class="hour-item"
                    :class="{ active: currentHour === hour }"
                    @click="selectHour(hour)"
                >
                    <div class="hour-point"></div>
                    <div class="hour-label">{{ formatHour(hour) }}</div>
                </div>
            </div>
            <div class="progress-bar">
                <div class="progress-fill" :style="{ width: progressWidth + '%' }"></div>
            </div>
        </div>
    </div>
</template>

<script>
export default {
    name: 'hourly-timeline',
    props: {
        // 初始小时
        initialHour: {
            type: Number,
            default: 1
        },
        // 轮播间隔（毫秒）
        interval: {
            type: Number,
            default: 2000
        },
        // 手动点击后的暂停时间（毫秒）
        pauseDuration: {
            type: Number,
            default: 2000
        },
        // 是否自动开始播放
        autoStart: {
            type: Boolean,
            default: true
        }
    },
    data() {
        return {
            currentHour: this.initialHour,
            isAutoPlaying: false,
            autoPlayTimer: null,
            pauseTimer: null,
            hours: Array.from({ length: 24 }, (_, i) => i + 1)
        };
    },
    computed: {
        progressWidth() {
            return ((this.currentHour - 1) / 23) * 100;
        }
    },
    mounted() {
        if (this.autoStart) {
            this.startAutoPlay();
        }
    },
    beforeDestroy() {
        this.stopAutoPlay();
        this.clearPauseTimer();
    },
    methods: {
        formatHour(hour) {
            return hour.toString().padStart(2, '0') + ':00';
        },
        selectHour(hour) {
            this.currentHour = hour;
            this.$emit('hour-change', hour);

            // 手动点击时暂停自动播放
            if (this.isAutoPlaying) {
                this.pauseAutoPlay();
            }
        },
        toggleAutoPlay() {
            if (this.isAutoPlaying) {
                this.stopAutoPlay();
            } else {
                this.startAutoPlay();
            }
        },
        startAutoPlay() {
            this.isAutoPlaying = true;
            this.autoPlayTimer = setInterval(() => {
                this.nextHour();
            }, this.interval);
        },
        stopAutoPlay() {
            this.isAutoPlaying = false;
            if (this.autoPlayTimer) {
                clearInterval(this.autoPlayTimer);
                this.autoPlayTimer = null;
            }
        },
        pauseAutoPlay() {
            this.stopAutoPlay();
            this.clearPauseTimer();

            // 设置暂停计时器，暂停结束后恢复自动播放
            this.pauseTimer = setTimeout(() => {
                this.startAutoPlay();
            }, this.pauseDuration);
        },
        clearPauseTimer() {
            if (this.pauseTimer) {
                clearTimeout(this.pauseTimer);
                this.pauseTimer = null;
            }
        },
        nextHour() {
            const nextHour = this.currentHour >= 24 ? 1 : this.currentHour + 1;
            this.currentHour = nextHour;
            this.$emit('hour-change', nextHour);
        },
        resetToHour(hour = 1) {
            this.currentHour = hour;
            this.$emit('hour-change', hour);
        }
    }
};
</script>

<style lang="less" scoped>
.hourly-timeline {
    background: rgba(0, 42, 92, 0.3);
    border-radius: 6px;
    padding: 16px;
    margin-bottom: 16px;

    .timeline-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 16px;

        .timeline-title {
            color: #fff;
            font-size: 14px;
            font-weight: 500;
        }

        .timeline-controls {
            .el-button {
                color: #0095ff;
                padding: 4px 8px;

                &.active {
                    color: #fff;
                }

                &:hover {
                    color: #fff;
                }
            }
        }
    }

    .timeline-container {
        position: relative;

        .timeline-track {
            display: flex;
            justify-content: space-between;
            align-items: center;
            position: relative;
            padding: 8px 0;

            .hour-item {
                display: flex;
                flex-direction: column;
                align-items: center;
                cursor: pointer;
                transition: all 0.3s ease;

                .hour-point {
                    width: 8px;
                    height: 8px;
                    border-radius: 50%;
                    background: rgba(255, 255, 255, 0.4);
                    margin-bottom: 4px;
                    transition: all 0.3s ease;
                }

                .hour-label {
                    font-size: 10px;
                    color: rgba(255, 255, 255, 0.6);
                    transition: all 0.3s ease;
                }

                &:hover {
                    .hour-point {
                        background: #0095ff;
                        transform: scale(1.2);
                    }

                    .hour-label {
                        color: #fff;
                    }
                }

                &.active {
                    .hour-point {
                        background: #0095ff;
                        transform: scale(1.4);
                        box-shadow: 0 0 8px rgba(0, 149, 255, 0.6);
                    }

                    .hour-label {
                        color: #0095ff;
                        font-weight: 500;
                    }
                }
            }
        }

        .progress-bar {
            position: absolute;
            bottom: 16px;
            left: 0;
            right: 0;
            height: 2px;
            background: rgba(255, 255, 255, 0.2);
            border-radius: 1px;

            .progress-fill {
                height: 100%;
                background: linear-gradient(90deg, #0095ff, #00d4ff);
                border-radius: 1px;
                transition: width 0.3s ease;
            }
        }
    }
}
</style>
