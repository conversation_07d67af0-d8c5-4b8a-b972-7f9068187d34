@import '../global.less';
@pbutton-bg-color: linear-gradient(180deg, #2ab1c7 0%, #105181 48%, #1b90f3 100%);
.el-button {
    font-size: 0.78rem;
    &--primary {
        background: linear-gradient(180deg, #2ab1c7 0%, #105181 48%, #1b90f3 100%);
        opacity: 0.81;
        border-radius: 0.28rem;
        color: #e8fdff;
        border: 1px solid #47c0ff;
        &:hover,
        &:focus {
            background-color: @pbutton-bg-color;
            border-color: @pbutton-bg-color;
        }

        &:active {
            background-color: darken(@secondary-color, 10%);
            border-color: darken(@secondary-color, 10%);
        }

        &.is-disabled,
        &.is-disabled:hover,
        &.is-disabled:focus,
        &.is-disabled:active {
            background-color: lighten(@secondary-color, 20%);
            border-color: lighten(@secondary-color, 20%);
        }
    }

    &--success {
        background-color: #67c23a;
        border-color: #67c23a;

        &:hover,
        &:focus {
            background-color: #85ce61;
            border-color: #85ce61;
        }

        &:active {
            background-color: #5daf34;
            border-color: #5daf34;
        }
    }

    &--warning {
        background-color: #e6a23c;
        border-color: #e6a23c;

        &:hover,
        &:focus {
            background-color: #ebb563;
            border-color: #ebb563;
        }

        &:active {
            background-color: #cf9236;
            border-color: #cf9236;
        }
    }

    &--danger {
        background-color: #f56c6c;
        border-color: #f56c6c;

        &:hover,
        &:focus {
            background-color: #f78989;
            border-color: #f78989;
        }

        &:active {
            background-color: #dd6161;
            border-color: #dd6161;
        }
    }

    &--info {
        background-color: #909399;
        border-color: #909399;

        &:hover,
        &:focus {
            background-color: #a6a9ad;
            border-color: #a6a9ad;
        }

        &:active {
            background-color: #82848a;
            border-color: #82848a;
        }
    }

    // 朴素按钮样式
    &.is-plain {
        &.el-button--primary {
            color: @secondary-color;
            background-color: lighten(@secondary-color, 35%);
            border-color: lighten(@secondary-color, 20%);

            &:hover,
            &:focus {
                background-color: @secondary-color;
                border-color: @secondary-color;
                color: #fff;
            }
        }
    }

    // 圆角按钮
    &.is-round {
        border-radius: 20px;
    }

    // 圆形按钮
    &.is-circle {
        border-radius: 50%;
        padding: 12px;
    }
}
