import { SHANDONG_CITY } from '@/script/constant/shandong';

const communityFormCols = [
    {
        prop: 'cityId',
        label: '地市名称',
        element: 'el-select',
        attrs: {
            placeholder: '请选择',
            clearable: true,
            'popper-class': 'gdb-select-dropdown-dark gdb-popover-dark'
        },
        slot: {
            element: 'el-option',
            enums: SHANDONG_CITY()
        },
        span: 4
    },
    {
        prop: 'roadName',
        label: '道路名称',
        element: 'el-input',
        attrs: {
            placeholder: '请输入',
            clearable: true
        },
        span: 4
    },
    {
        span: 16
    }
];

const communityTableCols = [
    {
        prop: 'cityName',
        label: '地市名称'
    },
    {
        prop: 'roadName',
        label: '道路名称'
    },
    {
        prop: 'gridId',
        label: '网格ID'
    },
    {
        prop: 'updateTime',
        label: '更新时间',
        'min-width': 180
    },
    {
        prop: 'operation',
        label: '操作',
        width: 180
    }
];

export { communityFormCols, communityTableCols };
