<template>
    <div class="content">
        <Card title="23G地铁小区基础信息">
            <div class="form-wrapper">
                <BaseForm
                    ref="baseForm"
                    :form-data="formData"
                    :form-config="formConfig"
                    :rules="rules"
                    :action="action"
                    :visible-fields="nowFields"
                    @submit="handleSubmit"
                />
            </div>
        </Card>
    </div>
</template>

<script>
import Card from '../../../components/Card/index.vue';
import BaseForm from '../../../components/BaseForm/index.vue';
import { SHANDONG_CITY } from '@/script/constant/shandong';
import { getLabelByValue, ruleValidatorWGS84 } from '@/script/utils/method.js';
import {
    COVER_TYPE,
    DATA_TYPE
} from '@/script/constant/cityRoadNetwork/23GMetroCommunityManager.js';

export default {
    name: 'detail-content',
    components: {
        Card,
        BaseForm
    },
    props: {
        row: {
            type: Object,
            default: () => ({})
        },
        action: {
            type: String,
            default: 'check'
        }
    },
    data() {
        return {
            nowFields: {
                add: [
                    'dataType',
                    'eci',
                    'cityid',
                    'roadsitename',
                    'linename',
                    'covertype',
                    'communityLatLng'
                ],
                edit: [
                    'dataType',
                    'eci',
                    'cityid',
                    'roadsitename',
                    'linename',
                    'covertype',
                    'communityLatLng'
                ],
                check: [
                    'dataType',
                    'eci',
                    'cityid',
                    'roadsitename',
                    'linename',
                    'covertype',
                    'communityLatLng'
                ]
            },
            formData: {
                dataType: '',
                eci: '',
                cityid: '',
                roadsitename: '',
                linename: '',
                covertype: '',
                communityLatLng: ''
            },
            formConfig: [
                {
                    prop: 'dataType',
                    label: '数据类型：',
                    type: 'el-select',
                    placeholder: '请选择数据类型',
                    options: DATA_TYPE,
                    attrs: {
                        clearable: true,
                        'popper-class': 'gdb-select-dropdown-dark gdb-popover-dark'
                    },
                    formatter: (value) => getLabelByValue(DATA_TYPE, value)
                },
                {
                    prop: 'eci',
                    label: '小区ECI：',
                    type: 'input',
                    placeholder: '请输入小区ECI',
                    attrs: {
                        clearable: true,
                        disabled: this.action === 'edit'
                    }
                },
                {
                    prop: 'cityid',
                    label: '地市名称：',
                    type: 'el-select',
                    placeholder: '请选择地市名称',
                    options: SHANDONG_CITY(),
                    attrs: {
                        clearable: true,
                        'popper-class': 'gdb-select-dropdown-dark gdb-popover-dark'
                    },
                    formatter: (value) => getLabelByValue(SHANDONG_CITY(), value)
                },
                {
                    prop: 'roadsitename',
                    label: '地铁站点名称：',
                    type: 'input',
                    placeholder: '请输入地铁站点名称',
                    attrs: {
                        clearable: true
                    }
                },
                {
                    prop: 'linename',
                    label: '归属线路：',
                    type: 'input',
                    placeholder: '请输入归属线路',
                    attrs: {
                        clearable: true
                    }
                },
                {
                    prop: 'covertype',
                    label: '覆盖类型：',
                    type: 'el-select',
                    placeholder: '请选择覆盖类型',
                    options: COVER_TYPE,
                    attrs: {
                        clearable: true,
                        'popper-class': 'gdb-select-dropdown-dark gdb-popover-dark'
                    },
                    formatter: (value) => getLabelByValue(COVER_TYPE, value)
                },
                {
                    prop: 'communityLatLng',
                    label: '小区经纬度：',
                    type: 'input',
                    placeholder: '请输入小区经纬度（格式：经度,纬度）',
                    attrs: {
                        clearable: true
                    }
                }
            ],
            rules: {
                dataType: [{ required: true, message: '请选择数据类型', trigger: 'change' }],
                eci: [
                    { required: this.action !== 'edit', message: '请输入小区ECI', trigger: 'blur' }
                ],
                cityid: [{ required: true, message: '请选择地市名称', trigger: 'change' }],
                roadsitename: [{ required: true, message: '请输入地铁站点名称', trigger: 'blur' }],
                linename: [{ required: true, message: '请输入归属线路', trigger: 'blur' }],
                covertype: [{ required: true, message: '请选择覆盖类型', trigger: 'change' }],
                communityLatLng: [
                    { required: true, message: '请输入小区经纬度', trigger: 'blur' },
                    { validator: ruleValidatorWGS84, trigger: 'blur' }
                ]
            }
        };
    },
    created() {
        if (['edit', 'check'].includes(this.action) && this.row) {
            this.formData = {
                ...this.formData,
                ...this.row,
                cityid: this.row.cityid.toString(),
                covertype: this.row.covertype.toString(),
                communityLatLng: this.row.celllongitude + ',' + this.row.celllatitude
            };
        }
    },
    methods: {
        handleSubmit(form, modeText) {
            // 只需更改apiPathMap接口路径以及添加payload
            const apiPathMap = new Map([
                ['add', 'subway/community/addSubwayCommunity'],
                ['edit', 'subway/community/updateSubwayCommunity']
            ]);

            const payload = {
                add: {
                    cityid: Number(form.cityid) || undefined,
                    cityname: getLabelByValue(SHANDONG_CITY(), form.cityid),
                    roadsitename: form.roadsitename,
                    linename: form.linename,
                    eci: form.eci,
                    covertype: +form.covertype,
                    dataType: form.dataType,
                    celllongitude: form.communityLatLng.split(',')[0],
                    celllatitude: form.communityLatLng.split(',')[1]
                },
                edit: {
                    roadsite: this.row.roadsite,
                    cityid: Number(form.cityid) || undefined,
                    cityname: getLabelByValue(SHANDONG_CITY(), form.cityid),
                    roadsitename: form.roadsitename,
                    linename: form.linename,
                    eci: this.row.eci,
                    covertype: +form.covertype,
                    dataType: form.dataType,
                    celllongitude: form.communityLatLng.split(',')[0],
                    celllatitude: form.communityLatLng.split(',')[1]
                }
            };

            $request(
                'post',
                `mtexapi/region-service/${apiPathMap.get(this.action)}`,
                payload[this.action]
            ).then(({ serviceFlag, returnMsg }) => {
                if (serviceFlag === 'TRUE') {
                    this.$emit('goBack');
                    this.$message({
                        type: 'success',
                        message: `${modeText}成功`
                    });
                    return;
                }
                this.$message({
                    type: 'error',
                    message: returnMsg
                });
            });
        }
    }
};
</script>

<style lang="less" scoped>
.content {
    min-height: 100%;
    height: max-content;
    display: flex;
    flex-direction: column;

    .form-wrapper {
        display: flex;
        justify-content: center;
        align-items: center;
    }
}
</style>
