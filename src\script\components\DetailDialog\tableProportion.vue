<template>
    <div class="content-container">
        <div class="table-header">
            <div class="header-item rank">排行</div>
            <div class="header-item address">常住地</div>
            <div class="header-item number">人数</div>
            <div class="header-item percent">占比</div>
        </div>
        <div class="table-body">
            <div class="table-row" v-for="(item, index) in tableData" :key="index">
                <div class="cell rank">
                    <span class="rank-number" :class="'rank-' + (index + 1)">{{ index + 1 }}</span>
                </div>
                <div class="cell address">{{ item.address }}</div>
                <div class="cell number">{{ item.number }}</div>
                <div class="cell percent">
                    <div class="progress-bar">
                        <div class="progress" :style="{ width: item.percent + '%' }"></div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</template>

<script>
export default {
    props: {
        resultData: {
            type: Array,
            default: () => []
        }
    },
    watch: {
        resultData: {
            handler(newVal) {
                console.log(newVal);
            },
            deep: true
        }
    },
    data() {
        return {
            tableData: [
                { address: '历下区', number: 1830, percent: 35 },
                { address: '市中区', number: 1830, percent: 35 },
                { address: '槐荫区', number: 1830, percent: 35 },
                { address: '天桥区', number: 1830, percent: 35 },
                { address: '历城区', number: 1830, percent: 35 }
            ]
        };
    }
};
</script>

<style lang="less" scoped>
.content-container {
    width: 100%;
    height: 100%;
    padding: 2.22rem;

    .table-header {
        display: grid;
        grid-template-columns: 80px 1fr 1fr 2fr;
        padding: 0.78rem 0 2.22rem;
        color: #c3dff6;
        font-size: 1.11rem;
        .header-item {
            padding: 0 0.56rem;
        }
    }

    .table-body {
        color: #c3dff6;
        font-size: 1.11rem;
        .table-row {
            display: grid;
            grid-template-columns: 80px 1fr 1fr 2fr;
            align-items: center;
            padding: 0.89rem 0;

            &:hover {
                background-color: #013b70;
            }

            .cell {
                padding: 0 1rem;

                &.rank {
                    .rank-number {
                        display: inline-block;
                        width: 1.33rem;
                        height: 1.33rem;
                        line-height: 1.33rem;
                        text-align: center;
                        border-radius: 0.22rem;
                        background-color: #e9ecef;
                        color: #666;

                        &.rank-1 {
                            background-color: #4b8bf4;
                            color: #fff;
                        }
                        &.rank-2 {
                            background-color: #64b5f6;
                            color: #fff;
                        }
                        &.rank-3 {
                            background-color: #81c784;
                            color: #fff;
                        }
                    }
                }

                &.number {
                    color: #c3dff6;
                    font-weight: 500;
                }

                .progress-bar {
                    width: 100%;
                    height: 0.44rem;
                    background-color: #e9ecef;
                    border-radius: 4px;
                    overflow: hidden;

                    .progress {
                        height: 100%;
                        background-color: #4b8bf4;
                        border-radius: 0.22rem;
                        transition: width 0.3s ease;
                    }
                }
            }
        }
    }
}
</style>
