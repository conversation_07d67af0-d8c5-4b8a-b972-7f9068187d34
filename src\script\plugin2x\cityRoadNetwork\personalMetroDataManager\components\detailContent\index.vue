<template>
    <div class="content">
        <Card title="地铁出行汇总登记">
            <div class="form-wrapper">
                <BaseForm
                    ref="baseForm"
                    :form-data="formData"
                    :form-config="formConfig"
                    :rules="rules"
                    :action="action"
                    :visible-fields="nowFields"
                    @submit="handleSubmit"
                />
            </div>
        </Card>
    </div>
</template>

<script>
import Card from '../../../components/Card/index.vue';
import BaseForm from '../../../components/BaseForm/index.vue';
import { SHANDONG_CITY } from '@/script/constant/shandong';
import { getLabelByValue } from '@/script/utils/method.js';

export default {
    name: 'detail-content',
    components: {
        Card,
        BaseForm
    },
    props: {
        row: {
            type: Object,
            default: () => ({})
        },
        action: {
            type: String,
            default: 'check'
        }
    },
    data() {
        return {
            statisticType: '3', // 默认为天汇总统计
            nowFields: {
                add: [
                    'statisticType',
                    'time',
                    'cityId',
                    'subwayStationId',
                    'flowInCnt',
                    'flowOutCnt',
                    'currentCnt'
                ],
                edit: [
                    'statisticType',
                    'time',
                    'cityId',
                    'subwayStationId',
                    'flowInCnt',
                    'flowOutCnt',
                    'currentCnt'
                ],
                check: [
                    'statisticType',
                    'time',
                    'cityId',
                    'subwayStationId',
                    'flowInCnt',
                    'flowOutCnt',
                    'currentCnt'
                ]
            },
            formData: {
                time: '',
                cityId: '',
                cityName: '',
                subwayStationId: '',
                flowInCnt: '',
                flowOutCnt: '',
                currentCnt: ''
            },
            metroOptions: []
        };
    },
    computed: {
        formConfig() {
            return [
                {
                    prop: 'time',
                    label: '时间：',
                    type: 'el-date-picker',
                    placeholder: '请选择时间',
                    attrs: {
                        clearable: true,
                        type: { 3: 'date', 2: 'month' }[this.statisticType],
                        format: { 3: 'yyyy-MM-dd', 2: 'yyyy-MM' }[this.statisticType],
                        'value-format': { 3: 'yyyyMMdd', 2: 'yyyyMM' }[this.statisticType],
                        'popper-class': 'gdb-date-picker-dark gdb-popover-dark',
                        class: 'date-editor-padding',
                        'picker-options': {
                            disabledDate: (time) => {
                                return time.getTime() > Date.now();
                            }
                        },
                        disabled: this.action === 'edit'
                    }
                },
                {
                    prop: 'cityId',
                    label: '地市名称：',
                    type: 'el-select',
                    placeholder: '请选择地市名称',
                    options: SHANDONG_CITY({ type: 'metro' }),
                    attrs: {
                        clearable: true,
                        'popper-class': 'gdb-select-dropdown-dark gdb-popover-dark',
                        disabled: this.action === 'edit'
                    },
                    formatter: (value) => getLabelByValue(SHANDONG_CITY({ type: 'metro' }), value)
                },
                {
                    prop: 'subwayStationId',
                    label: '地铁站点名称：',
                    type: 'el-select',
                    placeholder: '请先选择地市名称',
                    options: this.metroOptions,
                    attrs: {
                        clearable: true,
                        filterable: true,
                        'popper-class': 'gdb-select-dropdown-dark gdb-popover-dark',
                        disabled: this.action === 'edit'
                    },
                    formatter: (value) => getLabelByValue(this.metroOptions, value)
                },
                {
                    prop: 'flowInCnt',
                    label: '流入人数：',
                    type: 'input',
                    placeholder: '请输入流入人数',
                    attrs: {
                        clearable: true
                    }
                },
                {
                    prop: 'flowOutCnt',
                    label: '流出人数：',
                    type: 'input',
                    placeholder: '请输入流出人数',
                    attrs: {
                        clearable: true
                    }
                },
                {
                    prop: 'currentCnt',
                    label: '总人数：',
                    type: 'input',
                    placeholder: '请输入总人数',
                    attrs: {
                        clearable: true
                    }
                }
            ];
        },
        rules() {
            return {
                time: [{ required: true, message: '请选择时间', trigger: 'change' }],
                cityId: [{ required: true, message: '请选择地市名称', trigger: 'change' }],
                subwayStationId: [
                    { required: true, message: '请输入地铁站点名称', trigger: 'blur' }
                ],
                flowInCnt: [
                    { required: true, message: '请输入流入人数', trigger: 'blur' },
                    { validator: this.validateNumber, trigger: 'blur' }
                ],
                flowOutCnt: [
                    { required: true, message: '请输入流出人数', trigger: 'blur' },
                    { validator: this.validateNumber, trigger: 'blur' }
                ],
                currentCnt: [
                    { required: true, message: '请输入总人数', trigger: 'blur' },
                    { validator: this.validateNumber, trigger: 'blur' }
                ]
            };
        }
    },
    created() {
        if (['edit', 'check'].includes(this.action) && this.row) {
            this.formData = {
                ...this.formData,
                ...this.row,
                cityId: this.row.cityId.toString(),
                time: this.formatDateNumber(this.row.time)
            };
            this.statisticType = this.row.statisticType;
        }
        if (this.action === 'add') {
            this.statisticType = this.row.statisticType;
        }
    },
    watch: {
        'formData.cityId': {
            async handler(newVal) {
                if (!newVal) {
                    this.metroOptions = [];
                    return;
                }
                const { data: res } = await $request(
                    'post',
                    'mtexapi/region-service/personal/sub/info/config',
                    {
                        timeType: this.statisticType,
                        cityId: newVal
                    }
                );
                this.metroOptions = res.map((item) => {
                    return { label: item.subwayStationName, value: item.subwayStationId };
                });
            }
        }
    },
    methods: {
        formatDateNumber(date) {
            const dateStr = String(date);
            if (dateStr.length === 6) {
                return `${dateStr.slice(0, 4)}-${dateStr.slice(4)}`; // YYYY-MM
            } else if (dateStr.length === 8) {
                return `${dateStr.slice(0, 4)}-${dateStr.slice(4, 6)}-${dateStr.slice(6)}`; // YYYY-MM-DD
            }
        },
        validateNumber(rule, value, callback) {
            const val = Number(value);
            if (isNaN(val)) {
                callback(new Error('请输入数字'));
            } else if (!Number.isInteger(val)) {
                callback(new Error('请输入整数'));
            } else if (val < 0) {
                callback(new Error('请输入大于0的整数'));
            } else {
                callback();
            }
        },
        handleSubmit(form, modeText) {
            // 只需更改apiPathMap接口路径以及添加payload
            const apiPathMap = new Map([
                ['add', 'personal/sub/info/add'],
                ['edit', 'personal/sub/info/update']
            ]);

            const payload = {
                add: {
                    time: form.time.replace(/-/g, ''),
                    cityName: getLabelByValue(SHANDONG_CITY({ type: 'metro' }), form.cityId),
                    cityId: +form.cityId,
                    subwayStationId: form.subwayStationId,
                    subwayStationName: getLabelByValue(this.metroOptions, form.subwayStationId),
                    flowInCnt: form.flowInCnt,
                    flowOutCnt: form.flowOutCnt,
                    currentCnt: form.currentCnt
                },
                edit: {
                    time: form.time.replace(/-/g, ''),
                    cityName: getLabelByValue(SHANDONG_CITY({ type: 'metro' }), form.cityId),
                    cityId: +form.cityId,
                    subwayStationId: form.subwayStationId,
                    subwayStationName: getLabelByValue(this.metroOptions, form.subwayStationId),
                    flowInCnt: form.flowInCnt,
                    flowOutCnt: form.flowOutCnt,
                    currentCnt: form.currentCnt
                }
            };

            $request(
                'post',
                `mtexapi/region-service/${apiPathMap.get(this.action)}`,
                payload[this.action]
            ).then(({ serviceFlag, returnMsg }) => {
                if (serviceFlag === 'TRUE') {
                    this.$emit('goBack');
                    this.$message({
                        type: 'success',
                        message: `${modeText}成功`
                    });
                    return;
                }
                this.$message({
                    type: 'error',
                    message: returnMsg
                });
            });
        }
    }
};
</script>

<style lang="less" scoped>
.content {
    min-height: 100%;
    height: max-content;
    display: flex;
    flex-direction: column;

    .form-wrapper {
        display: flex;
        justify-content: center;
        align-items: center;
    }
}
</style>
