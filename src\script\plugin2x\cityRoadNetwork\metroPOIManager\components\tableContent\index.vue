<template>
    <commonTableContent
        ref="metroPOIListTable"
        :fields="fields"
        :initialForm="initialForm"
        addButtonText="新增站点POI"
        :columns="columns"
        :data="tableData"
        :dataTotal="total"
        @get-table-data="handleGetTableData"
        @action="handleAction"
        @delete="handleDelete"
    >
        <template #stationType="{ row }">
            <span>{{ stationType(row.stationType) }}</span>
        </template>
        <template #belongLine="{ row }">
            <span>{{ metroLine(row.belongLine) }}</span>
        </template>
    </commonTableContent>
</template>

<script>
import commonTableContent from '../../../components/commonTableContent/index.vue';
import {
    siteFormCols,
    siteTableCols,
    stationTypes,
    metroLines
} from '@/script/constant/cityRoadNetwork/metroPOIManager.js';

export default {
    name: 'table-content',
    components: {
        commonTableContent
    },
    data() {
        return {
            initialForm: {
                siteName: '',
                updateTime: null,
                siteType: '',
                belongLine: ''
            },
            total: 0,
            tableData: []
        };
    },
    computed: {
        fields() {
            return siteFormCols;
        },
        columns() {
            return siteTableCols;
        },
        stationType() {
            return (val) => {
                const type = stationTypes.find((item) => item.value === val);
                if (type) {
                    return type.label;
                }
                return val;
            };
        },
        metroLine() {
            return (val) => {
                const line = metroLines.find((item) => item.value === val);
                if (line) {
                    return line.label;
                }
                return val;
            };
        }
    },
    methods: {
        async handleGetTableData(params, pagination) {
            const validator = (e) => {
                if (e === null || e === undefined || e === '') {
                    return undefined;
                }
                return e;
            };
            const payload = {
                pageNo: pagination.curPage,
                pageSize: pagination.pageSize,
                stationName: validator(params.siteName),
                stationType: validator(params.siteType),
                belongLine: validator(params.belongLine),
                startTime: (params.updateTime && params.updateTime[0]) || undefined,
                endTime: (params.updateTime && params.updateTime[1]) || undefined
            };

            let { data } = await $request(
                'post',
                'mtexapi/region-service/management/stations/query',
                payload
            );
            this.tableData = data.data.map((item) => ({
                ...item,
                id: item.id,
                stationName: item.stationName,
                stationType: item.stationType,
                belongLine: item.belongLine,
                stationTags: item.stationLabel,
                metroPOI: item.longitude + ',' + item.latitude,
                lastUpdateTime: item.updateTime,
                lastUpdateUser: item.updateUser
            }));
            this.total = data.total;
        },
        handleAction(action, row = {}) {
            // 将action转发到父组件
            this.$emit('action', action, row);
        },
        handleDelete(row) {
            this.$confirm('确定要删除吗？', '提示', {
                confirmButtonText: '确定',
                cancelButtonText: '取消',
                type: 'warning'
            }).then(() => {
                $request('post', 'mtexapi/region-service/management/stations/delete', {
                    ids: [row.id]
                })
                    .then(({ serviceFlag, returnMsg }) => {
                        if (serviceFlag === 'TRUE') {
                            this.refreshTable();
                            this.$message({
                                type: 'success',
                                message: '删除成功'
                            });
                        } else {
                            this.$message({
                                type: 'error',
                                message: returnMsg
                            });
                        }
                    })
                    .catch(() => {});
            });
        },
        refreshTable() {
            this.$refs.metroPOIListTable.search();
        }
    },
    mounted() {
        this.refreshTable();
    }
};
</script>
