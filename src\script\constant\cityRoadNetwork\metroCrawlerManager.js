import { SHANDONG_CITY } from '@/script/constant/shandong';

const SCRAPE_OBJECT = [
    { label: '地铁站点', value: '地铁站点' },
    { label: '地铁线路', value: '地铁线路' }
];

// 任务状态（1:已完成, 2:进行中, 3:待启动, 4:失败）
const TASK_STATUS = [
    { label: '已完成', value: 1 },
    { label: '进行中', value: 2 },
    { label: '待启动', value: 3 },
    { label: '失败', value: 4 }
];

const formCols = [
    {
        prop: 'scrapeObject',
        label: '对象选择',
        element: 'el-select',
        attrs: {
            placeholder: '请选择',
            clearable: true,
            'popper-class': 'gdb-select-dropdown-dark gdb-popover-dark'
        },
        slot: {
            element: 'el-option',
            enums: SCRAPE_OBJECT
        },
        span: 4
    },
    {
        prop: 'cityCode',
        label: '城市',
        element: 'el-select',
        attrs: {
            placeholder: '请选择',
            clearable: true,
            'popper-class': 'gdb-select-dropdown-dark gdb-popover-dark'
        },
        slot: {
            element: 'el-option',
            enums: SHANDONG_CITY()
        },
        span: 4
    },
    {
        prop: 'timeRange',
        label: '完成时间',
        element: 'el-date-picker',
        attrs: {
            clearable: true,
            type: 'daterange',
            'range-separator': '-',
            'start-placeholder': '开始日期',
            'end-placeholder': '结束日期',
            format: 'yyyy-MM-dd',
            'value-format': 'yyyy-MM-dd',
            'popper-class': 'gdb-date-picker-dark gdb-popover-dark'
            // 'picker-options': {
            //     disabledDate: (time) => {
            //         return time.getTime() > Date.now();
            //     }
            // }
        },
        span: 6
    },
    {
        span: 10
    }
];

const tableCols = [
    {
        prop: 'taskName',
        label: '任务名称'
    },
    {
        prop: 'cityCode',
        label: '任务名称城市'
    },
    {
        prop: 'scrapeObject',
        label: '爬取对象'
    },
    {
        prop: 'taskStatus',
        label: '任务状态'
    },
    {
        prop: 'scrapeCount',
        label: '爬取数量'
    },
    {
        prop: 'completionTime',
        label: '完成时间',
        'min-width': 180
    },
    {
        prop: 'operation',
        label: '操作',
        width: 180
    }
];

export { formCols, tableCols, SCRAPE_OBJECT, TASK_STATUS };
