import axios from 'axios';
import api from '@/script/api';
import { decodeResultUser, tryEncodeParam } from './resUtils';
//去除mtex
let baseURL = systemUtil.rootPath.replace('/mtex', '');
window.cancelTokenCollect = {};
const removeCancelToken = (urlName, source) => {
    const targetIndex = window.cancelTokenCollect[urlName].findIndex((item) => item === source);
    window.cancelTokenCollect[urlName].splice(targetIndex, 1);
};
window.cancelRequest = (urlName) => {
    window.cancelTokenCollect[urlName] &&
        window.cancelTokenCollect[urlName].forEach((item) => {
            item.cancel('中断请求：' + urlName);
        });
};
const server = axios.create({
    timeout: 180000,
    baseURL: baseURL + '/'
});
const encryption = true; // todo

server.interceptors.request.use(
    (config) => {
        if (config.url === 'auth/micro/verifyUserRolesByRoleName') {
            config.headers.common['token'] = localStorage.getItem('token');
        }
        if (config.method.toUpperCase() === 'POST') {
            if (config.headers['content-type'] !== 'multipart/form-data') {
                config.headers['content-type'] = 'application/json';
                config.data = JSON.stringify({ ...config.data });
            }
        }
        return config;
    },
    (err) => {
        Promise.reject(err);
    }
);

server.interceptors.response.use(
    (res) => {
        let newRes = res.data;
        if (res.status === 200) {
            //判断是否正确返回
            if (encryption) {
                if (newRes && newRes.encodeResp) {
                    newRes = decodeResultUser(res.data);
                }
            }
            if (newRes.success || newRes.errcode === 0) {
                return newRes.data;
            } else if (
                res.headers &&
                [
                    'application/vnd.ms-excel;charset=utf-8',
                    'application/octet-stream;charset=UTF-8'
                ].includes(res.headers['content-type'])
            ) {
                return res;
            }
            return Promise.resolve(newRes);
        }
        return Promise.reject(newRes.msg || newRes.errmsg || newRes.message);
    },
    (err) => {
        return Promise.reject(err);
    }
);

const request = async (method, urlName, data = {}, downloadProgressFn) => {
    if (!window.cancelTokenCollect[urlName]) {
        window.cancelTokenCollect[urlName] = [];
    }
    if (urlName !== 'getRoles') {
        data.token = localStorage.getItem('token');
    }
    const CancelToken = axios.CancelToken;
    const source = CancelToken.source();
    window.cancelTokenCollect[urlName].push(source);
    if (encryption) {
        data = tryEncodeParam(data);
    }
    if (method.toUpperCase() === 'GET') {
        return new Promise(function (resolve, reject) {
            server
                .get(`${baseURL}/${api[urlName] || urlName}`, {
                    params: data,
                    cancelToken: source.token,
                    onDownloadProgress: downloadProgressFn
                })
                .then((res) => {
                    resolve(res);
                })
                .catch((err) => {
                    if (axios.isCancel(err)) {
                        console.log('Request canceled', err.message);
                    } else {
                        reject(err);
                    }
                });
        }).finally(() => {
            removeCancelToken(urlName, source);
        });
    } else if (method.toUpperCase() === 'POST') {
        return new Promise(function (resolve, reject) {
            server
                .post(`${baseURL}/${api[urlName] || urlName}`, data, {
                    cancelToken: source.token,
                    onDownloadProgress: downloadProgressFn
                })
                .then((res) => {
                    resolve(res);
                })
                .catch((err) => {
                    if (axios.isCancel(err)) {
                        console.log('Request canceled', err.message);
                    } else {
                        let options = {
                            title: '消息提示',
                            content: '接口请求失败！',
                            detail: `详细内容：${err.errorMessage || err}`
                        };
                        new Vue().$popupMessageWindow(options);
                        reject(err);
                    }
                });
        }).finally(() => {
            removeCancelToken(urlName, source);
        });
    }
};

const getFileRequst = (method, urlName, data = {}, config = {}) => {
    // 判断是否是formData,如果是formData不加密
    const isFormData = config.headers && config.headers['content-type'] === 'multipart/form-data';
    if (isFormData) {
        data.append('token', localStorage.getItem('token'));
    } else {
        data.token = localStorage.getItem('token');
    }
    if (encryption && !isFormData) {
        data = tryEncodeParam(data);
    }
    return new Promise(function (resolve, reject) {
        server({
            method: method.toUpperCase(),
            url: `${baseURL}/${api[urlName] || urlName}`,
            data,
            ...config
        })
            .then((res) => {
                resolve(res);
            })
            .catch((err) => {
                reject(err);
            });
    });
};

export { request, getFileRequst };

export default (method, url, data = {}) => {
    if (method.toUpperCase() === 'GET') {
        return server.get(url, { params: data });
    } else if (method.toUpperCase() === 'POST') {
        return server.post(url, data);
    }
};
