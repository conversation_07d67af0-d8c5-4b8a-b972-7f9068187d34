<template>
    <el-form
        ref="searchBarRef"
        :class="customClass"
        :model="form"
        v-bind="{
            size: 'small',
            'label-width': '76px',
            inline: true,
            ...$attrs
        }"
        v-on="$listeners"
    >
        <el-row :gutter="gutter">
            <template
                v-for="(
                    {
                        prop,
                        label,
                        element,
                        attrs,
                        listeners,
                        slot,
                        rules,
                        size,
                        labelWidth,
                        itemClassName,
                        span = 24,
                        layout,
                        isShow = true,
                        isFull = true,
                        isDisabled
                    },
                    index
                ) in fields"
            >
                <el-col v-show="isShow" :key="prop || index" :span="span" v-bind="layout">
                    <el-form-item
                        :class="{
                            itemClassName,
                            isTransparent: !prop && !element,
                            isDisabled
                        }"
                        v-bind="{
                            label,
                            prop,
                            rules,
                            size,
                            labelWidth
                        }"
                    >
                        <template #error="{ error }">
                            <span class="custom-error">
                                {{ error }}
                            </span>
                        </template>
                        <!-- 组件 -->
                        <component
                            v-if="element"
                            :is="element"
                            :class="{ 'w-full': isFull }"
                            v-model="form[prop]"
                            v-bind="attrs"
                            v-on="listeners"
                        >
                            <!-- 基础类型 -->
                            <template v-if="isDetermineType(slot)">{{ slot }}</template>
                            <!-- 引用类型 -->
                            <template v-else>
                                <component
                                    v-for="(it, inx) in processSlot(slot)"
                                    :is="slot.element || it.element"
                                    :key="inx"
                                    v-bind="it"
                                    v-on="it.listeners"
                                >
                                    <template v-if="it.text">{{ it.text }}</template>
                                </component>
                            </template>
                        </component>
                        <!-- 自定义插槽/默认插槽 -->
                        <template v-else>
                            <slot :name="prop || 'default'"></slot>
                        </template>
                    </el-form-item>
                </el-col>
            </template>
        </el-row>
    </el-form>
</template>
<script>
export default {
    name: 'search-bar',
    inheritAttrs: false,
    props: {
        fields: {
            type: Array,
            default: () => [],
            validator(value) {
                return Array.isArray(value) && value.length;
            }
        },
        form: {
            type: Object,
            validator(value) {
                return value instanceof Object && Object.keys(value).length;
            }
        },
        gutter: {
            type: Number,
            default: 12
        },
        customClass: {
            type: String,
            default: 'search-bar'
        }
    },
    methods: {
        async validForm() {
            try {
                return await this.$refs.searchBarRef.validate();
            } catch (err) {
                return err;
            }
        },
        // 基础数据类型
        isDetermineType(value) {
            return ['string', 'number', 'boolean'].includes(typeof value);
        },
        isObject(type) {
            return Object.prototype.toString.call(type) === '[object Object]';
        },
        processSlot(slot) {
            switch (true) {
                case Array.isArray(slot):
                    return slot;
                case this.isObject(slot) && slot.element && Array.isArray(slot.enums):
                    slot.enums.element = slot.element;
                    return slot.enums;
                case this.isObject(slot):
                    return [slot];
                default:
                    return [];
            }
        }
    },
    async deactivated() {
        if (this.$listeners.validate) {
            const res = await this.validForm();
            this.$emit('validate', res);
        }
    }
};
</script>
<style lang="less" scoped>
.search-bar {
    width: 100%;
    .el-row {
        display: flex;
        flex-flow: wrap;
        .el-col:last-of-type .el-form-item {
            margin-right: 0;
        }
        .el-form-item {
            display: flex;
            margin: 0 0 12px 0;
            background-color: transparent;

            /deep/ .el-form-item__label {
                font-family: SourceHanSansCN, SourceHanSansCN;
                font-weight: bold;
                font-size: 14px;
                color: rgba(255, 255, 255, 0.65);
            }

            .custom-error {
                position: absolute;
                right: 0;
                top: -26px;
                padding: 0 6px;
                height: 20px;
                line-height: 20px;
                border-radius: 2px;
                font-size: 12px;
                color: #fff;
                background-color: #f56c6c;
                z-index: 999;
                &::before {
                    content: '';
                    position: absolute;
                    margin-top: -5px;
                    width: 10px;
                    height: 8px;
                    right: 50%;
                    bottom: -3px;
                    transform: translateX(50%) rotate(45deg);
                    background-color: #f56c6c;
                }
            }
            &.isTransparent {
                background-color: transparent;
            }
            &.isDisabled {
                /deep/ .el-form-item__label {
                    background-color: #f5f7fa;
                }
                /deep/ .el-input__inner {
                    background-color: #f5f7fa;
                    cursor: not-allowed;
                }
            }
            /deep/ .el-form-item__label {
                position: relative;
                margin-bottom: 0;
                padding-right: 10px;
                &::before {
                    margin-right: 2px !important;
                }
                // label 竖线
                &::after {
                    content: '：';
                    position: absolute;
                }
            }
            /deep/ .el-form-item__content {
                position: relative;
                display: flex;
                align-items: center;
                flex: 1;
            }
            /deep/ .el-date-editor--month {
                .el-input__inner {
                    padding: 0 30px;
                    border-radius: 0;
                    // border: none;
                    background-color: transparent;
                }
            }
        }
    }
}
.w-full {
    width: 100%;
}
</style>
