<template>
    <commonTableContent
        ref="stationCommunityListTable"
        :fields="fields"
        :initialForm="initialForm"
        addButtonText="添加站点小区"
        :columns="columns"
        :data="tableData"
        :dataTotal="total"
        @get-table-data="handleGetTableData"
        @action="handleAction"
        @delete="handleDelete"
    >
        <template #city="{ row }">
            <span>{{ getLabelByValue(SHANDONG_CITY, row.city) }}</span>
        </template>
        <template #district="{ row }">
            <span>{{ getLabelByValue(SHANDONG_DISTRICT(row.city), row.district) }}</span>
        </template>
        <template #highSpeed="{ row }">
            <span>{{ row.highSpeed ? '是' : '否' }}</span>
        </template>
        <template #intercity="{ row }">
            <span>{{ row.intercity ? '是' : '否' }}</span>
        </template>
        <template #moving="{ row }">
            <span>{{ row.moving ? '是' : '否' }}</span>
        </template>
        <template #regular="{ row }">
            <span>{{ row.regular ? '是' : '否' }}</span>
        </template>
    </commonTableContent>
</template>

<script>
import commonTableContent from '../../../components/commonTableContent/index.vue';
import {
    communityFormCols,
    communityTableCols
} from '@/script/constant/cityRoadNetwork/stationCommunityManager.js';
import { getLabelByValue, getPositionData } from '@/script/utils/method.js';
import { SHANDONG_CITY, SHANDONG_DISTRICT } from '@/script/constant/shandong';

export default {
    name: 'table-content',
    components: {
        commonTableContent
    },
    data() {
        return {
            initialForm: {
                stationName: '',
                cityId: ''
            },
            total: 0,
            tableData: [],
            stationOptions: [],
            getLabelByValue,
            getPositionData
        };
    },
    computed: {
        fields() {
            return communityFormCols;
        },
        columns() {
            return communityTableCols;
        },
        SHANDONG_CITY() {
            return SHANDONG_CITY();
        },
        SHANDONG_DISTRICT() {
            return (cityId) => SHANDONG_DISTRICT({ cityId });
        }
    },
    created() {
        this.getStationOptions();
    },
    methods: {
        async getStationOptions() {
            const { data: res } = await $request(
                'post',
                'mtexapi/region-service/trainStation/region/config'
            );
            this.stationOptions = res.map((item) => {
                return { label: item.regionName, value: item.regionId };
            });
        },
        async handleGetTableData(params, pagination) {
            const payload = {
                trainStationName: params.stationName,
                cityId: params.cityId,
                pageNo: pagination.curPage,
                pageSize: pagination.pageSize
            };

            let { data } = await $request(
                'post',
                'mtexapi/region-service/station/trainStationLaccell/query',
                payload
            );
            this.tableData = data.list;
            this.total = data.total;
        },
        handleAction(action, row = {}) {
            // 将action转发到父组件
            this.$emit('action', action, { ...row, stationOptions: this.stationOptions });
        },
        handleDelete(row) {
            // 删除逻辑
            this.$confirm('确定要删除吗？', '提示', {
                confirmButtonText: '确定',
                cancelButtonText: '取消',
                type: 'warning'
            })
                .then(() => {
                    $request('post', 'mtexapi/region-service/station/trainStationLaccell/delete', {
                        trainStationId: row.trainStationId
                    })
                        .then(({ serviceFlag, returnMsg }) => {
                            if (serviceFlag === 'TRUE') {
                                this.refreshTable();
                                this.$message({
                                    type: 'success',
                                    message: '删除成功'
                                });
                            } else {
                                this.$message({
                                    type: 'error',
                                    message: returnMsg
                                });
                            }
                        })
                        .catch(() => {});
                })
                .catch(() => {
                    // 用户取消删除
                });
        },
        refreshTable() {
            this.getStationOptions();
            this.$refs.stationCommunityListTable.search();
        }
    },
    mounted() {
        this.refreshTable();
    }
};
</script>
