const wgs84Regex = /^(-?\d+(\.\d+)?),\s*(-?\d+(\.\d+)?)$/;

// 验证是否正确的WGS84坐标
export const validateWGS84 = (coordinate) => {
    // 首先用正则检查格式是否正确
    const match = wgs84Regex.exec(coordinate);
    if (!match) {
        return false;
    }

    // 提取经度和纬度的值
    const longitude = parseFloat(match[1]);
    const latitude = parseFloat(match[3]);

    // 检查经度和纬度是否在有效范围内
    return longitude >= -180 && longitude <= 180 && latitude >= -90 && latitude <= 90;
};

// 表单内校验函数
export const ruleValidatorWGS84 = (rule, value, callback) => {
    if (!value) {
        callback();
        return;
    }
    if (!validateWGS84(value)) {
        callback(new Error('请输入正确的坐标格式(经度,纬度)，经度范围-180~180，纬度范围-90~90'));
        return;
    }
    callback();
};

// 获取下拉框值对应名称
export const getLabelByValue = (list, value) => {
    const item = list.find((item) => item.value == value);
    if (item) {
        return item.label;
    }
    return value;
};

// 下载文件
export function handleDownloadFile(res, callback, fileName) {
    if (res.data.type === 'application/json') {
        const errBlob = new Blob([res.data], {
            type: 'application/json'
        });
        let reader = new FileReader();
        reader.readAsText(errBlob, 'utf-8');
        reader.addEventListener('loadend', function () {
            //
            let text = JSON.parse(reader.result);
            callback(text.returnMsg || '下载失败');
        });
        return;
    }
    const blob = new Blob([res.data], {
        type: 'application/vnd.ms-excel'
    });
    if (!fileName) {
        fileName = decodeURI(res.headers['content-disposition'].split('filename=')[1]);
    }
    const linkNode = document.createElement('a');
    linkNode.download = fileName;
    linkNode.style.display = 'none';
    linkNode.href = URL.createObjectURL(blob);
    document.body.appendChild(linkNode);
    linkNode.click();
    URL.revokeObjectURL(linkNode.href);
    document.body.removeChild(linkNode);
}

/**
 * 时间格式化工具（根据输入长度自动决定默认格式）
 * @param {string} timeStr - 时间字符串，如 '20250609110359' 或 '20250609' 等
 * @param {string} [format] - 可选，自定义输出格式
 * @returns {string} 格式化后的时间字符串
 */
export function formatTime(timeStr, format) {
    // 根据输入长度确定默认格式
    if (!format) {
        switch (timeStr.length) {
            case 4: // 年
                format = 'YYYY';
                break;
            case 6: // 年月
                format = 'YYYY-MM';
                break;
            case 8: // 年月日
                format = 'YYYY-MM-DD';
                break;
            case 10: // 年月日时
                format = 'YYYY-MM-DD HH';
                break;
            case 12: // 年月日时分
                format = 'YYYY-MM-DD HH:mm';
                break;
            case 14: // 年月日时分秒
                format = 'YYYY-MM-DD HH:mm:ss';
                break;
            default:
                format = 'YYYY-MM-DD HH:mm:ss';
        }
    }

    // 补全时间字符串为完整格式（14位）
    let paddedTime = timeStr.padEnd(14, '0');

    // 解析时间各部分
    const year = paddedTime.substring(0, 4);
    const month = paddedTime.substring(4, 6) || '01';
    const day = paddedTime.substring(6, 8) || '01';
    const hours = paddedTime.substring(8, 10) || '00';
    const minutes = paddedTime.substring(10, 12) || '00';
    const seconds = paddedTime.substring(12, 14) || '00';

    // 格式化替换
    const replacements = {
        YYYY: year,
        MM: month,
        DD: day,
        HH: hours,
        mm: minutes,
        ss: seconds
    };

    // 替换格式字符串中的占位符
    let result = format;
    for (const [key, value] of Object.entries(replacements)) {
        result = result.replace(key, value);
    }

    return result;
}

/**
 * 获取位置数据
 * @param {number} code - 位置编码
 */
export const getPositionData = async (code = 370000) => {
    const { data } = await $request('post', 'mtexapi/region-service/common/card/cities', {
        districtCode: Number(code)
    });
    const dataMap = data.map((item) => ({
        label: item.key,
        value: item.value
    }));
    return dataMap;
};
