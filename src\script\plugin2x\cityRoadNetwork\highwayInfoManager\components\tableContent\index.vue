<template>
    <commonTableContent
        ref="highwayInfoListTable"
        :fields="fields"
        :initialForm="initialForm"
        addButtonText="导入"
        :columns="columns"
        :data="tableData"
        :dataTotal="total"
        @get-table-data="handleGetTableData"
        @action="handleAction"
        @delete="handleDelete"
    >
    </commonTableContent>
</template>

<script>
import commonTableContent from '../../../components/commonTableContent/index.vue';
import {
    infoFormCols,
    infoTableCols
} from '@/script/constant/cityRoadNetwork/highwayInfoManager.js';

export default {
    name: 'table-content',
    components: {
        commonTableContent
    },
    data() {
        return {
            initialForm: {
                cityName: '',
                highwayName: ''
            },
            total: 0,
            tableData: []
        };
    },
    computed: {
        fields() {
            return infoFormCols;
        },
        columns() {
            return infoTableCols;
        }
    },
    methods: {
        async handleGetTableData(params, pagination) {
            const payload = {
                cityName: params.cityName,
                roadName: params.highwayName,
                pageNo: pagination.curPage,
                pageSize: pagination.pageSize
            };

            let { data } = await $request(
                'post',
                'mtexapi/region-service/regionsubscribe/highWayDetails/getRoadList',
                payload
            );
            this.tableData = data.list;
            this.total = data.total;
        },
        handleAction(action, row = {}) {
            // 将action转发到父组件
            this.$emit('action', action, row);
        },
        handleDelete(row) {
            // 删除逻辑
            this.$confirm('确定要删除吗？', '提示', {
                confirmButtonText: '确定',
                cancelButtonText: '取消',
                type: 'warning'
            })
                .then(() => {
                    $request(
                        'post',
                        'mtexapi/region-service/regionsubscribe/highWayDetails/deleteRoadInfo',
                        {
                            roadSubIds: row.roadSubIds
                        }
                    )
                        .then(({ serviceFlag, returnMsg }) => {
                            if (serviceFlag === 'TRUE') {
                                this.refreshTable();
                                this.$message({
                                    type: 'success',
                                    message: '删除成功'
                                });
                            } else {
                                this.$message({
                                    type: 'error',
                                    message: returnMsg
                                });
                            }
                        })
                        .catch(() => {});
                })
                .catch(() => {
                    // 用户取消删除
                });
        },
        refreshTable() {
            this.$refs.highwayInfoListTable.search();
        }
    },
    mounted() {
        this.refreshTable();
    }
};
</script>
