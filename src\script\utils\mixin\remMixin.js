let mixin = {
    data() {
        return {
            originalFs: ''
        };
    },
    watch: {
        '$store.state.windowSize': function (value) {
            this.setRootFontSize('into');
        }
    },
    created() {
        this.setRootFontSize('into');
    },
    destroyed() {
        this.setRootFontSize('leave');
    },
    activated() {
        this.setRootFontSize('into');
    },
    deactivated() {
        this.setRootFontSize('leave');
    },
    methods: {
        getRootFontSize() {
            let width = parseInt(this.$store.state.windowSize.split('x')[0]),
                proportion = 1920 / 18,
                rootFontSize = width / proportion;
            return rootFontSize;
        },
        setRootFontSize(state) {
            if (state === 'leave') {
                document.documentElement.style.fontSize = this.originalFs;
            } else {
                let rootFontSize = this.getRootFontSize();
                document.documentElement.style.fontSize = rootFontSize + 'px';
            }
        }
    }
};
export default mixin;
