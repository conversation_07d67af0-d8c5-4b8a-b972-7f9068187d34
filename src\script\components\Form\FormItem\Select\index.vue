<template>
    <el-select
        :style="[$attrs.itemStyle || {}]"
        v-model="currentSelect"
        @change="changeSelect"
        :class="addClass"
        v-bind="$attrs"
        :popper-append-to-body="false"
        popper-class="diySelectPanel"
        class="form-select"
    >
        <el-option
            v-for="item of options"
            :key="item.key"
            :label="item.label"
            :value="item.key"
            v-bind="item"
        >
        </el-option>
    </el-select>
</template>

<script>
export default {
    name: 'RamsSelect',
    model: {
        prop: 'value',
        event: 'change'
    },
    props: {
        value: {
            type: [String, Number, Boolean],
            default: () => ''
        },
        addClass: {
            type: [Object, Array, String]
        },
        slotNames: {
            type: Array
        },
        options: {
            type: Array,
            default: () => []
        },
        defaultVal: {
            type: [Number, String],
            default: ''
        }
    },

    data() {
        return {
            currentSelect: this.defaultVal || this.value
        };
    },
    watch: {
        value(newVal) {
            this.currentSelect = newVal;
        }
    },

    methods: {
        changeSelect(newVale) {
            this.$emit('change', newVale);
        }
    }
};
</script>
<style lang="less" scoped>
.form-select {
    /deep/.el-input__inner {
        background: #013b70;
        border: 1px solid #47c0ff;
        color: #91aac1;
        font-weight: 500;
    }
}
/deep/ .diySelectPanel {
    background: #013b70;
    box-shadow: inset 0px -1px 0px 0px rgba(64, 155, 255, 0.3);
    border-radius: 0.11rem;
    border: 1px solid rgba(53, 165, 255, 0.6);
    color: #c9dfff;
    .el-select-dropdown__item {
        color: #c9dfff;
        font-weight: 500;
        font-family:
            Source Han Sans CN,
            Source Han Sans CN;
        font-size: 0.78rem;
        &:hover,
        .selected {
            background: #128bcf;
            color: #00f5f2;
        }
    }
    .el-select-dropdown__item.hover {
        background: #128bcf;
        color: #00f5f2;
    }
    .popper__arrow {
        display: none;
    }
}
</style>
