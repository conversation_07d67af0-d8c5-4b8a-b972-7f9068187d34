.page-container {
    width: 100%;
    height: 100%;
    display: flex;
    flex-direction: column;
    background: #012249;
    padding: 1.67rem 2.78rem;

    .main-content {
        flex: 1;
        display: flex;
        overflow: hidden;

        .content-section {
            flex: 1;
            padding: 0 2.22rem 1.11rem;
            overflow-y: auto;
        }
        /* 定义滚动条样式 */
        ::-webkit-scrollbar {
            width: 0.44rem;
            height: 0.44rem; /* 滚动条上的滚动滑块 */
        }

        ::-webkit-scrollbar-thumb {
            background-color: #04fbfd;
            border-radius: 1.78rem;
        }
    }
}
