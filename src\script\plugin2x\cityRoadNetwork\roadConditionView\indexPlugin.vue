<template>
    <div class="page-container">
        <Header :nav="navOptions" @goBack="goBack" />
        <div class="main-content">
            <tableGisContent
                ref="tableGisContent"
                @action="handleAction"
                v-show="['table'].includes(nowAction)"
            />
        </div>
    </div>
</template>

<script>
import Header from '../components/Header/index.vue';
import tableGisContent from './components/tableGisContent/index.vue';

export default {
    name: 'BusRouteManager',
    components: {
        Header,
        tableGisContent
    },
    data() {
        return {
            navOptions: {
                showBack: true,
                beforePathName: '',
                lastPathName: '城市道路路况展示'
            },
            nowAction: 'table', // 当前页面状态，table: 表格页面，add: 添加页面，edit: 编辑页面，check: 查看页面
            row: {}
        };
    },
    watch: {
        nowAction: {
            handler(newVal) {
                if (!this.$route.query.gdb_appId && this.nowAction === 'table') {
                    this.navOptions.showBack = false;
                }
            },
            immediate: true
        }
    },
    methods: {
        goBack() {
            // 页面是由展板点击进入且当前页面是表格页面时，返回按钮返回展板页面
            if (this.$route.query.gdb_appId && this.nowAction === 'table') {
                if (this.$route.query.appId) {
                    // 展板页面是被嵌套的，则返回展板页的父页面
                    frameService.appOpenById(this.$route.query.appId, {
                        sd_appId: this.$route.query.gdb_appId
                    });
                } else {
                    // 展板页面是独立页面，则返回展板页面
                    frameService.appOpenById(this.$route.query.gdb_appId);
                }
                return;
            }
            this.updateNavigation({
                ...this.$options.data().navOptions,
                action: 'table'
            });
        },
        updateNavigation({ showBack, beforePathName, lastPathName, action }) {
            this.navOptions.showBack = showBack;
            this.navOptions.beforePathName = beforePathName;
            this.navOptions.lastPathName = lastPathName;
            this.nowAction = action;
            this.nowAction === 'table' && this.$refs.tableGisContent.refreshTable();
        },
        handleAction(action, row = {}) {
            this.row = row;
            const actionConfig = {
                add: {
                    lastPathName: '添加公交线路'
                },
                edit: {
                    lastPathName: '修改公交线路'
                },
                check: {
                    lastPathName: '公交线路详情'
                }
            };
            // 目前只考虑两级深度路径路由
            if (action in actionConfig) {
                this.updateNavigation({
                    showBack: true,
                    beforePathName: `${this.$options.data().navOptions.lastPathName} /`,
                    lastPathName: actionConfig[action].lastPathName,
                    action
                });
            }
        }
    }
};
</script>

<style lang="less" scoped>
@import '../common.less';
.page-container {
    width: 100%;
    height: 100%;
    display: flex;
    flex-direction: column;
    background: linear-gradient(180deg, #f2f9fe 0%, #f8fafe 100%);
    padding: 30px 50px 10px 50px;
    overflow: auto;
}
.main-content {
    flex: 1;
    height: 0;
}
</style>
