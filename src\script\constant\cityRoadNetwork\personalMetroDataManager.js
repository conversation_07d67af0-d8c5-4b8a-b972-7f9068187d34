import { SHANDONG_CITY } from '@/script/constant/shandong';
const typeOptions = [
    { label: '天汇总统计', value: '3' },
    { label: '月汇总统计', value: '2' }
];

const infoFormCols = ({ fun = () => {}, nowTab = '3' }) => {
    return [
        {
            prop: 'statisticType',
            element: 'el-tabs',
            isFull: false,
            attrs: {
                placeholder: '请输入',
                clearable: true
            },
            slot: {
                element: 'el-tab-pane',
                enums: typeOptions.map((item) => {
                    return { label: item.label, name: item.value };
                })
            },
            listeners: {
                'tab-click': (tab, event) => {
                    fun(tab.name);
                }
            },
            span: 4
        },
        {
            prop: 'cityId',
            label: '地市名称',
            element: 'el-select',
            attrs: {
                placeholder: '请选择',
                clearable: true,
                'popper-class': 'gdb-select-dropdown-dark gdb-popover-dark'
            },
            slot: {
                element: 'el-option',
                enums: SHANDONG_CITY({ type: 'metro' })
            },
            span: 4
        },
        {
            prop: 'stationName',
            label: '地铁站点名称',
            labelWidth: '180',
            element: 'el-input',
            attrs: {
                placeholder: '请输入',
                clearable: true
            },
            span: 4
        },
        {
            prop: 'timeRange',
            label: '时间范围',
            element: 'el-date-picker',
            attrs: {
                clearable: true,
                type: { 3: 'daterange', 2: 'monthrange' }[nowTab],
                key: nowTab,
                'range-separator': '-',
                'start-placeholder': '开始日期',
                'end-placeholder': '结束日期',
                format: { 3: 'yyyy-MM-dd', 2: 'yyyy-MM' }[nowTab],
                'value-format': { 3: 'yyyyMMdd', 2: 'yyyyMM' }[nowTab],
                'popper-class': 'gdb-date-picker-dark gdb-popover-dark',
                'picker-options': {
                    disabledDate: (time) => {
                        return time.getTime() > Date.now();
                    }
                }
            },
            span: 6
        },
        {
            span: 6
        }
    ];
};

const infoTableCols = [
    {
        prop: 'time',
        label: '时间'
    },
    {
        prop: 'cityName',
        label: '地市名称'
    },
    {
        prop: 'subwayStationName',
        label: '地铁站点名称'
    },
    {
        prop: 'flowInCnt',
        label: '流入人数'
    },
    {
        prop: 'flowOutCnt',
        label: '流出人数'
    },
    {
        prop: 'currentCnt',
        label: '总人数'
    },
    {
        prop: 'operation',
        label: '操作',
        width: 180
    }
];

export { infoFormCols, infoTableCols };
