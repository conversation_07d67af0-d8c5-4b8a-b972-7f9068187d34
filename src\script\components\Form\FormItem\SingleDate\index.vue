<template>
    <el-date-picker
        :append-to-body="false"
        :style="[$attrs.itemStyle || {}]"
        placeholder="请选择日期"
        :class="addClass"
        type="date"
        :format="format"
        value-format="yyyy-MM-dd"
        :picker-options="pickerOptions"
        v-model="currentVal"
        v-bind="$attrs"
        v-on="$listeners"
        @change="changeInput"
    >
    </el-date-picker>
</template>

<script>
export default {
    name: 'RamsSingleDate',
    model: {
        prop: 'value',
        event: 'change'
    },
    props: {
        value: {
            type: [String, Date]
        },
        addClass: {
            type: [Object, Array, String]
        },
        pickerOptions: {
            type: Object,
            default: () => ({})
        }
    },
    data() {
        return {
            currentVal: this.value,
            format: 'yyyy-MM-dd'
        };
    },

    watch: {
        value(val) {
            this.currentVal = val;
        }
    },

    methods: {
        changeInput(newVal) {
            this.$emit('change', this.currentVal);
        }
    }
};
</script>
