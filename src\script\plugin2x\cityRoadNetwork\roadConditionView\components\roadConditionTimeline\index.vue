<template>
    <!-- 时间轴 -->
    <div class="road-condition-timeline">
        <div class="label-name">时间轴</div>
        <div class="timeline-axis">
            <div class="arrow-left" @click="previousHour"></div>
            <div class="time-axis">
                <div
                    v-for="hour in 24"
                    :key="hour"
                    class="hour-item"
                    :class="{ 'active-hour': currentHour === hour }"
                    @click="selectHour(hour)"
                >
                    <span class="time-text">{{ hour + '点' }}</span>
                </div>
            </div>
            <div class="arrow-right" @click="nextHour"></div>
        </div>
    </div>
</template>

<script>
export default {
    name: 'road-condition-timeline',
    props: {
        // 初始小时
        initialHour: {
            type: Number,
            default: 1
        },
        // 轮播间隔（毫秒）
        interval: {
            type: Number,
            default: 2000
        },
        // 手动点击后的暂停时间（毫秒）
        pauseDuration: {
            type: Number,
            default: 2000
        },
        // 是否自动开始播放
        autoStart: {
            type: Boolean,
            default: true
        }
    },
    data() {
        return {
            currentHour: this.initialHour,
            // 时间轴功能相关
            isAutoPlaying: false,
            autoPlayTimer: null,
            pauseTimer: null
        };
    },
    watch: {
        initialHour: {
            handler(newHour) {
                this.currentHour = newHour;
            },
            immediate: true
        }
    },
    mounted() {
        if (this.autoStart) {
            this.$nextTick(() => {
                if (!this.isAutoPlaying) {
                    this.startAutoPlay();
                }
            });
        }
    },
    beforeDestroy() {
        this.stopAutoPlay();
        this.clearPauseTimer();
    },
    methods: {
        // 时间轴功能方法
        selectHour(hour) {
            this.currentHour = hour;
            this.$emit('hour-change', hour);

            // 手动点击时暂停自动播放
            if (this.isAutoPlaying) {
                this.pauseAutoPlay();
            }
        },
        previousHour() {
            let prevHour;
            if (this.currentHour <= 1) {
                prevHour = 24;
            } else {
                prevHour = this.currentHour - 1;
            }
            this.selectHour(prevHour);
        },
        nextHour() {
            let nextHour;
            if (this.currentHour >= 24) {
                nextHour = 1;
            } else {
                nextHour = this.currentHour + 1;
            }
            console.log(`时间轴轮播: ${this.currentHour} -> ${nextHour}`);
            this.currentHour = nextHour;
            this.$emit('hour-change', nextHour);
        },
        startAutoPlay() {
            // 先停止现有的定时器，避免重复启动
            this.stopAutoPlay();
            this.isAutoPlaying = true;
            this.autoPlayTimer = setInterval(() => {
                this.nextHour();
            }, this.interval);
        },
        stopAutoPlay() {
            this.isAutoPlaying = false;
            if (this.autoPlayTimer) {
                clearInterval(this.autoPlayTimer);
                this.autoPlayTimer = null;
            }
        },
        pauseAutoPlay() {
            this.stopAutoPlay();
            this.clearPauseTimer();

            // 设置暂停计时器，暂停结束后恢复自动播放
            this.pauseTimer = setTimeout(() => {
                this.startAutoPlay();
            }, this.pauseDuration);
        },
        clearPauseTimer() {
            if (this.pauseTimer) {
                clearTimeout(this.pauseTimer);
                this.pauseTimer = null;
            }
        },
        resetToHour(hour = 1) {
            this.currentHour = hour;
            this.$emit('hour-change', hour);
        },
        // 外部方法接口
        reset(hour = 1) {
            this.stopAutoPlay();
            this.resetToHour(hour);
            this.startAutoPlay();
        },
        start() {
            this.startAutoPlay();
        },
        stop() {
            this.stopAutoPlay();
        }
    }
};
</script>

<style lang="less" scoped>
.road-condition-timeline {
    position: absolute;
    left: 60px;
    right: 60px;
    bottom: 12px;
    // width: 132px;
    height: 24px;
    display: flex;
    align-items: center;
    gap: 12px;
    .label-name {
        background-image: url('~@/img/cityRoadNet/select-bg.png');
        background-size: 100% 100%;
        background-repeat: no-repeat;
        padding: 0 10px;
        line-height: 24px;
        font-size: 14px;
        color: #fff;
        text-align: center;
        user-select: none;
    }
    .timeline-axis {
        min-width: 0;
        flex: 1;
        height: 100%;
        background-image: url('~@/img/cityRoadNet/time-axis.png');
        background-size: 98% 100%;
        background-repeat: no-repeat;
        background-position: center;
        user-select: none;
        position: relative;

        .arrow-left,
        .arrow-right {
            position: absolute;
            top: 50%;
            transform: translateY(-50%);
            width: 18px;
            height: 28px;
            background-size: 100% 100%;
            background-repeat: no-repeat;
            cursor: pointer;
        }
        .arrow-left {
            left: 0;
            background-image: url('~@/img/cityRoadNet/arrow-left.png');
        }
        .arrow-right {
            right: 0;
            background-image: url('~@/img/cityRoadNet/arrow-right.png');
        }

        .time-axis {
            width: 100%;
            height: 100%;
            padding: 0 24px;
            display: flex;
            justify-content: space-between;
            .hour-item {
                min-width: 0;
                max-width: 24px;
                flex-shrink: 1;
                display: flex;
                justify-content: center;
                width: 24px;
                height: 24px;
                background-image: url('~@/img/cityRoadNet/blue-point.png');
                background-size: 100% 100%;
                background-repeat: no-repeat;
                cursor: pointer;
                user-select: none;
                position: relative;
                .time-text {
                    position: absolute;
                    top: 0;
                    transform: translateY(-125%);
                    font-family: SourceHanSansCN-Medium, SourceHanSansCN-Medium;
                    font-weight: normal;
                    font-size: 11px;
                    color: #acb5cc;
                    line-height: 12px;
                    text-align: center;
                    font-style: normal;
                    text-transform: none;
                }
                &.active-hour {
                    background-image: url('~@/img/cityRoadNet/orange-point.png');
                    .time-text {
                        color: #5df2ff;
                    }
                }
            }
        }
    }
}
</style>
