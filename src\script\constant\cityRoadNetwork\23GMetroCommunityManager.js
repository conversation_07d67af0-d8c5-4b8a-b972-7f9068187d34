import { SHANDONG_CITY } from '@/script/constant/shandong';
const COVER_TYPE = [
    { label: '室内', value: '1' },
    { label: '室外', value: '2' }
];
const DATA_TYPE = [
    { label: '2G', value: '1' },
    { label: '3G', value: '2' }
];
const infoFormCols = [
    {
        prop: 'communityECI',
        label: '小区ECI',
        element: 'el-input',
        attrs: {
            placeholder: '请输入',
            clearable: true
        },
        span: 4
    },
    {
        prop: 'cityId',
        label: '地市名称',
        element: 'el-select',
        attrs: {
            placeholder: '请选择',
            clearable: true,
            'popper-class': 'gdb-select-dropdown-dark gdb-popover-dark'
        },
        slot: {
            element: 'el-option',
            enums: SHANDONG_CITY({ type: 'metro' })
        },
        span: 4
    },
    {
        prop: 'stationName',
        label: '地铁站点名称',
        labelWidth: '180',
        element: 'el-input',
        attrs: {
            placeholder: '请输入',
            clearable: true
        },
        span: 4
    },
    {
        prop: 'coverType',
        label: '覆盖类型',
        element: 'el-select',
        attrs: {
            placeholder: '请选择',
            clearable: true,
            'popper-class': 'gdb-select-dropdown-dark gdb-popover-dark'
        },
        slot: {
            element: 'el-option',
            enums: COVER_TYPE
        },
        span: 4
    },
    {
        prop: 'dataType',
        label: '数据类型',
        element: 'el-select',
        attrs: {
            placeholder: '请选择',
            clearable: false,
            'popper-class': 'gdb-select-dropdown-dark gdb-popover-dark'
        },
        slot: {
            element: 'el-option',
            enums: DATA_TYPE
        },
        span: 4
    },
    {
        span: 4
    }
];

const infoTableCols = [
    {
        prop: 'dataType',
        label: '数据类型'
    },
    {
        prop: 'eci',
        label: '小区ECI'
    },
    {
        prop: 'cityname',
        label: '地市名称'
    },
    {
        prop: 'roadsitename',
        label: '地铁站点名称'
    },
    {
        prop: 'linename',
        label: '归属线路'
    },
    {
        prop: 'covertype',
        label: '覆盖类型'
    },
    {
        prop: 'communityLatLng',
        label: '小区中心经纬度',
        'min-width': 180
    },
    {
        prop: 'createtime',
        label: '创建时间',
        'min-width': 180
    },
    {
        prop: 'username',
        label: '更新用户',
        'min-width': 180
    },
    {
        prop: 'operation',
        label: '操作',
        width: 180
    }
];

export { infoFormCols, infoTableCols, COVER_TYPE, DATA_TYPE };
