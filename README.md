# 位置能力-通用审计展板

## mtex-static-generaldisplayboard

## 目录结构

```bash
|- .vscode                    # vscode编辑器配置文件
|- build                      # webpack相关配置
|- src                        # 源代码目录
    |- img                    # 图片资源文件
    |- script                 # 脚本文件目录
        |- api               # 接口请求配置
        |- components        # 通用组件
        |- constant          # 常量配置
        |- enter             # 路由配置、全局配置
        |- plugin2x          # 业务模块vue代码
            |- displayBoard  # 展板模块
                |- components # 展板相关组件
                |- subModules # 展板相关子模块
            |- cityRoadNetwork # 城市路网模块
                |- components  # 城市路网相关组件
                |- ...         # 城市路网相关子模块
        |- utils             # 工具方法
    |- style                 # 全局样式文件
        |- global.less       # 全局变量和混入
|- .eslintignore             # eslint忽略文件配置
|- .eslintrc.js              # eslint配置
|- .gitignore                # git忽略文件配置
|- .yo-rc.json               # yeoman配置文件
|- package-lock.json         # 依赖版本锁定文件
|- package.json              # 项目配置文件
```

## 开发

### nginx 配置

```
server {
	#山东位置能力服务
	#监听的端口
	listen       5091;
	#监听的IP，注意这里不能用localhost，会很慢
	server_name  127.0.0.1;
	#转发框架后台，对应后台mtex-sys项目（0）
	location /mtex/ {
		proxy_pass http://************:31100/mtex/;
		proxy_set_header Host $http_host;
		proxy_set_header X-Real-IP $remote_addr;
		proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
		proxy_set_header Upgrade $http_upgrade;#websocket需要
		proxy_set_header Connection "upgrade";#websocket需要
		proxy_read_timeout 600s;#websocket连接超时时间，最好≥这个时间
	}
    # 前端文件定位到对应的文件位置
	location /mtex/static_dist/generaldisplayboardPlugin/ {
        proxy_pass http://127.0.0.1:8800/mtex/static_dist/generaldisplayboardPlugin/;
    }
}

登录账号密码：admin/MTwydsj@257

```

### 下载依赖

```sh
npm i
```

### 运行

```sh
npm run dev
```

### 构建

```sh
npm run prod
```
