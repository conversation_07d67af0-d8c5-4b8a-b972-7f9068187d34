<template>
    <commonTableContent
        ref="busRouteListTable"
        :fields="fields"
        :initialForm="initialForm"
        addButtonText=""
        :columns="columns"
        :data="tableData"
        :dataTotal="total"
        @get-table-data="handleGetTableData"
        @action="handleAction"
        @delete="handleDelete"
    >
    </commonTableContent>
</template>

<script>
import commonTableContent from '../../../components/commonTableContent/index.vue';
import {
    cellSiteFormCols,
    cellSiteTableCols
} from '@/script/constant/cityRoadNetwork/cellSiteRelationView.js';

export default {
    name: 'table-content',
    components: {
        commonTableContent
    },
    data() {
        return {
            initialForm: {
                cityId: '',
                subwayLine: undefined,
                coverageType: undefined,
                stationCategory: undefined
            },
            total: 0,
            tableData: []
        };
    },
    computed: {
        fields() {
            return cellSiteFormCols;
        },
        columns() {
            return cellSiteTableCols;
        }
    },
    methods: {
        async handleGetTableData(params, pagination) {
            const payload = {
                cityId: params.cityId,
                subwayLine: params.subwayLine,
                coverageType: params.coverageType,
                stationCategory: params.stationCategory,
                pageNo: pagination.curPage,
                pageSize: pagination.pageSize
            };

            let { data } = await $request(
                'post',
                'mtexapi/region-service/management/cellsite/relation/query',
                payload
            );
            this.tableData = data.data || data.list || [];
            this.total = data.total || 0;
        },
        handleAction(action, row = {}) {
            // 将action转发到父组件
            this.$emit('action', action, row);
        },
        handleDelete(row) {
            // 删除逻辑
            this.$confirm('确定要删除该基站小区关系记录吗？', '提示', {
                confirmButtonText: '确定',
                cancelButtonText: '取消',
                type: 'warning'
            })
                .then(() => {
                    $request('post', 'mtexapi/region-service/management/cellsite/relation/delete', {
                        ids: [row.cellEci]
                    })
                        .then(({ serviceFlag, returnMsg }) => {
                            if (serviceFlag === 'TRUE') {
                                this.refreshTable();
                                this.$message({
                                    type: 'success',
                                    message: '删除成功'
                                });
                            } else {
                                this.$message({
                                    type: 'error',
                                    message: returnMsg
                                });
                            }
                        })
                        .catch(() => {});
                })
                .catch(() => {
                    // 用户取消删除
                });
        },
        refreshTable() {
            this.$refs.busRouteListTable.search();
        }
    },
    mounted() {
        this.refreshTable();
    }
};
</script>
