.dashboard {
    padding: 20px;
    font-family:
        -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell,
        'Open Sans', 'Helvetica Neue', sans-serif;
}

.gender-stats {
    margin-bottom: 30px;
}

.user-icon {
    display: flex;
    align-items: center;
    gap: 20px;
}

.gender-numbers {
    color: #666;
    font-size: 14px;
}

.age-stats {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 20px;
}

.chart-container {
    position: relative;
    width: 200px;
    height: 200px;
}

.chart-info {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    text-align: center;
}

.percentage {
    display: block;
    font-size: 24px;
    font-weight: bold;
    color: #333;
}

.total {
    display: block;
    font-size: 20px;
    color: #4b8bf4;
    margin: 5px 0;
}

.label {
    display: block;
    font-size: 14px;
    color: #666;
}
