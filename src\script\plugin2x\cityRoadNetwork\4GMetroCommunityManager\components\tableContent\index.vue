<template>
    <commonTableContent
        ref="_4GMetroCommunityListTable"
        :fields="fields"
        :initialForm="initialForm"
        addButtonText="新增小区信息"
        :columns="columns"
        :data="tableData"
        :dataTotal="total"
        @get-table-data="handleGetTableData"
        @action="handleAction"
        @delete="handleDelete"
    >
        <!-- 自定义插槽 -->
        <template #dataType="{ row }">
            <span>{{ row.dataType == 3 ? '4G' : '' }}</span>
        </template>
        <template #covertype="{ row }">
            <span>{{ getLabelByValue(COVER_TYPE, row.covertype) }}</span>
        </template>
        <template #communityLatLng="{ row }">
            <span>{{ row.celllongitude }},{{ row.celllatitude }}</span>
        </template>
    </commonTableContent>
</template>

<script>
import commonTableContent from '../../../components/commonTableContent/index.vue';
import {
    infoFormCols,
    infoTableCols,
    COVER_TYPE
} from '@/script/constant/cityRoadNetwork/4GMetroCommunityManager.js';
import { getLabelByValue } from '@/script/utils/method.js';

export default {
    name: 'table-content',
    components: {
        commonTableContent
    },
    data() {
        return {
            initialForm: {
                communityECI: '',
                cityId: '',
                stationName: '',
                coverType: ''
            },
            total: 0,
            tableData: [],
            COVER_TYPE,
            getLabelByValue
        };
    },
    computed: {
        fields() {
            return infoFormCols;
        },
        columns() {
            return infoTableCols;
        }
    },
    methods: {
        async handleGetTableData(params, pagination) {
            const payload = {
                cityid: Number(params.cityId) || undefined,
                roadsitename: params.stationName,
                eci: params.communityECI,
                covertype: params.coverType,
                dataType: '3', // 代表4G写死
                pageNo: pagination.curPage,
                pageSize: pagination.pageSize
            };

            let { data } = await $request(
                'post',
                'mtexapi/region-service/subway/community/getPageList',
                payload
            );
            this.tableData = data.list;
            this.total = data.total;
        },
        handleAction(action, row = {}) {
            // 将action转发到父组件
            this.$emit('action', action, row);
        },
        handleDelete(row) {
            // 删除逻辑
            this.$confirm('确定要删除吗？', '提示', {
                confirmButtonText: '确定',
                cancelButtonText: '取消',
                type: 'warning'
            })
                .then(() => {
                    $request('post', 'mtexapi/region-service/subway/community/delSubwayCommunity', {
                        roadsite: row.roadsite
                    })
                        .then(({ serviceFlag, returnMsg }) => {
                            if (serviceFlag === 'TRUE') {
                                this.refreshTable();
                                this.$message({
                                    type: 'success',
                                    message: '删除成功'
                                });
                            } else {
                                this.$message({
                                    type: 'error',
                                    message: returnMsg
                                });
                            }
                        })
                        .catch(() => {});
                })
                .catch(() => {
                    // 用户取消删除
                });
        },
        refreshTable() {
            this.$refs._4GMetroCommunityListTable.search();
        }
    },
    mounted() {
        this.refreshTable();
    }
};
</script>
