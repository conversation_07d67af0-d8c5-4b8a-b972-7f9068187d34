<template>
    <div class="content">
        <Card title="线路信息">
            <div class="form-wrapper">
                <BaseForm
                    ref="baseForm"
                    :form-data="formData"
                    :form-config="formConfig"
                    :rules="rules"
                    :action="action"
                    :visible-fields="nowFields"
                    @submit="handleSubmit"
                />
            </div>
        </Card>
    </div>
</template>

<script>
import Card from '../../../components/Card/index.vue';
import BaseForm from '../../../components/BaseForm/index.vue';
import { getLabelByValue } from '@/script/utils/method.js';
import { SHANDONG_CITY } from '@/script/constant/shandong';

export default {
    name: 'detail-content',
    components: {
        Card,
        BaseForm
    },
    props: {
        row: {
            type: Object,
            default: () => ({})
        },
        action: {
            type: String,
            default: 'check'
        }
    },
    data() {
        return {
            formData: {
                line: '',
                lineName: undefined, // 作为主键
                cityId: ''
            },
            nowFields: {
                add: ['line', 'cityId'],
                edit: ['line', 'cityId'],
                check: ['line', 'cityId']
            },
            formConfig: [
                {
                    prop: 'line',
                    label: '线路名称：',
                    type: 'input',
                    placeholder: '请输入线路名称',
                    attrs: {
                        clearable: true
                    }
                },
                {
                    prop: 'cityId',
                    label: '归属城市：',
                    type: 'el-select',
                    placeholder: '请选择归属城市',
                    options: SHANDONG_CITY(),
                    attrs: {
                        clearable: true,
                        'popper-class': 'gdb-select-dropdown-dark gdb-popover-dark'
                    },
                    formatter: (value) => getLabelByValue(SHANDONG_CITY(), value)
                }
            ],
            rules: {
                line: [{ required: true, message: '请输入线路名称', trigger: 'blur' }],
                cityId: [{ required: true, message: '请选择归属城市', trigger: 'change' }]
            }
        };
    },
    computed: {
        modeText() {
            const map = new Map([
                ['add', '新增'],
                ['edit', '修改'],
                ['check', '查看']
            ]);
            return map.get(this.action);
        },
        getCityName() {
            const type = SHANDONG_CITY().find((item) => item.value === this.formData.cityId);
            if (type) {
                return type.label;
            }
            return this.formData.cityId;
        }
    },
    methods: {
        handleSubmit(form, modeText) {
            const isEdit = this.action === 'edit';
            $request(
                'post',
                `mtexapi/region-service/management/bus/line/${isEdit ? 'update' : 'add'}`,
                {
                    line: form.line,
                    lineName: this.formData.lineName, //这个作为路线的主键
                    cityId: form.cityId,
                    cityName: getLabelByValue(SHANDONG_CITY(), form.cityId)
                }
            ).then(({ serviceFlag, returnMsg }) => {
                if (serviceFlag === 'TRUE') {
                    this.$emit('goBack');
                    this.$message({
                        type: 'success',
                        message: `${modeText}成功`
                    });
                    return;
                }
                this.$message({
                    type: 'error',
                    message: returnMsg
                });
            });
        }
    },
    created() {
        if (['edit', 'check'].includes(this.action) && this.row) {
            this.formData = { ...this.formData, ...this.row };
        }
        // 查看不做校验
        if (['check'].includes(this.action)) {
            this.rules = {};
        }
    }
};
</script>

<style lang="less" scoped>
.content {
    min-height: 100%;
    height: max-content;
    display: flex;
    flex-direction: column;

    .form-wrapper {
        display: flex;
        justify-content: center;
        align-items: center;
    }
}
</style>
