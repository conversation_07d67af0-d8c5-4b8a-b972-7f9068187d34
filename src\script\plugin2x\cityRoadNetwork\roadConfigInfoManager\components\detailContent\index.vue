<template>
    <div class="content">
        <Card title="道路配置信息">
            <div class="form-wrapper">
                <BaseForm
                    ref="baseForm"
                    :form-data="formData"
                    :form-config="formConfig"
                    :rules="rules"
                    :action="action"
                    :visible-fields="nowFields"
                    @submit="handleSubmit"
                />
            </div>
        </Card>
    </div>
</template>

<script>
import Card from '../../../components/Card/index.vue';
import BaseForm from '../../../components/BaseForm/index.vue';
import { SHANDONG_CITY } from '@/script/constant/shandong';
import { getLabelByValue } from '@/script/utils/method.js';

export default {
    name: 'detail-content',
    components: {
        Card,
        BaseForm
    },
    props: {
        row: {
            type: Object,
            default: () => ({})
        },
        action: {
            type: String,
            default: 'check'
        }
    },
    data() {
        return {
            nowFields: {
                add: ['cityId', 'roadName'],
                edit: ['roadName'],
                check: ['cityId', 'roadName', 'updateTime']
            },
            formData: {
                cityId: '',
                roadName: '',
                updateTime: ''
            },
            formConfig: [
                {
                    prop: 'cityId',
                    label: '地市名称：',
                    type: 'el-select',
                    placeholder: '请选择地市名称',
                    options: SHANDONG_CITY(),
                    attrs: {
                        clearable: true,
                        disabled: this.action === 'edit',
                        'popper-class': 'gdb-select-dropdown-dark gdb-popover-dark'
                    },
                    formatter: (value) => getLabelByValue(SHANDONG_CITY(), value)
                },
                {
                    prop: 'roadName',
                    label: '道路名称：',
                    type: 'input',
                    placeholder: '请输入道路名称',
                    attrs: {
                        clearable: true
                    }
                },
                {
                    prop: 'updateTime',
                    label: '更新时间：',
                    type: 'input',
                    placeholder: '更新时间',
                    attrs: {
                        readonly: true
                    }
                }
            ],
            rules: {
                cityId: [{ required: true, message: '请选择地市名称', trigger: 'change' }],
                roadName: [{ required: true, message: '请输入道路名称', trigger: 'blur' }]
            }
        };
    },
    created() {
        if (['edit', 'check'].includes(this.action) && this.row) {
            this.formData = { ...this.formData, ...this.row, cityId: this.row.cityId.toString() };
        }
    },
    methods: {
        handleSubmit(form, modeText) {
            // 只需更改apiPathMap接口路径以及添加payload
            const apiPathMap = new Map([
                ['add', 'cityAndRoadManagement/add'],
                ['edit', 'cityAndRoadManagement/update']
            ]);

            const payload = {
                add: {
                    cityId: Number(form.cityId) || undefined,
                    cityName: getLabelByValue(SHANDONG_CITY(), form.cityId),
                    roadName: form.roadName
                },
                edit: {
                    roadId: this.row.roadId,
                    roadName: form.roadName
                }
            };

            $request(
                'post',
                `mtexapi/region-service/${apiPathMap.get(this.action)}`,
                payload[this.action]
            ).then(({ serviceFlag, returnMsg }) => {
                if (serviceFlag === 'TRUE') {
                    this.$emit('goBack');
                    this.$message({
                        type: 'success',
                        message: `${modeText}成功`
                    });
                    return;
                }
                this.$message({
                    type: 'error',
                    message: returnMsg
                });
            });
        }
    }
};
</script>

<style lang="less" scoped>
.content {
    min-height: 100%;
    height: max-content;
    display: flex;
    flex-direction: column;

    .form-wrapper {
        display: flex;
        justify-content: center;
        align-items: center;
    }
}
</style>
