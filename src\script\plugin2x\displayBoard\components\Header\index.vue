<template>
    <div class="header">
        <div class="search">
            <el-input v-model="searchKey" placeholder="立即使用">
                <i slot="suffix" class="el-input__icon el-icon-search"></i>
            </el-input>
        </div>
    </div>
</template>

<script>
export default {
    name: 'Header',
    data() {
        return {
            searchKey: ''
        };
    },
    watch: {
        searchKey(val) {
            this.$emit('search', val);
        }
    }
};
</script>

<style lang="less" scoped>
.header {
    margin-bottom: 1.33rem;

    .search {
        .el-input {
            width: 24.44rem;

            /deep/ .el-input__inner {
                width: 24.44rem;
                border-radius: 1.11rem;
                padding-left: 1.11rem;
                height: 2.22rem;
                font-size: 0.78rem;
                border: 1px solid #47c0ff;
                background: #013b70;
                color: #78a7db;
            }

            /deep/ .el-input__icon {
                color: #47c0ff;
                font-size: 0.89rem;
                line-height: 2.22rem;
                font-weight: bold;
            }
            /deep/.el-input__suffix {
                right: 0.56rem;
            }
        }
    }
}
</style>
