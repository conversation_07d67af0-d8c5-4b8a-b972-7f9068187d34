<template>
    <el-form
        ref="form"
        :model="formData"
        :rules="checkRules"
        :label-width="labelWidth"
        class="base-form"
    >
        <template v-for="item in processedFormConfig">
            <!-- 默认表单项 -->
            <el-form-item
                v-if="!item.slot && shouldShow(item.prop)"
                :key="item.prop"
                :label="item.label"
                :prop="item.prop"
            >
                <template v-if="action === 'check'">
                    <!-- 查看模式 -->
                    <el-input :value="getDisplayValue(item)" readonly />
                </template>
                <template v-else>
                    <!-- 编辑/新增模式 -->
                    <component
                        :is="getComponentType(item.type)"
                        v-model="formData[item.prop]"
                        :placeholder="getPlaceholder(item)"
                        v-bind="item.attrs || {}"
                    >
                        <!-- 为select类型添加options -->
                        <template
                            v-if="['select', 'el-select'].includes(item.type) && item.options"
                        >
                            <el-option
                                v-for="option in item.options"
                                :key="option.value"
                                :label="option.label"
                                :value="option.value"
                            />
                        </template>
                    </component>
                </template>
            </el-form-item>

            <!-- 具名插槽表单项 -->
            <template v-if="item.slot && shouldShow(item.prop)">
                <slot
                    :name="item.slot"
                    :isShow="true"
                    :label="item.label"
                    :field="formData[item.prop]"
                    :prop="item.prop"
                    :action="action"
                />
            </template>
        </template>

        <!-- 表单底部按钮 -->
        <div v-if="showFooter && action !== 'check'" class="form-footer">
            <slot name="footer">
                <el-button type="primary" class="form-footer-btn" @click="submitForm"
                    >完成{{ modeText }}</el-button
                >
            </slot>
        </div>
    </el-form>
</template>

<script>
const COMPONENT_MAP = {
    input: 'el-input',
    select: 'el-select',
    date: 'el-date-picker'
    // 可以继续添加其他组件类型
};

export default {
    name: 'BaseForm',
    props: {
        formData: {
            type: Object,
            required: true
        },
        formConfig: {
            type: Array,
            required: true
        },
        rules: {
            type: Object,
            default: () => ({})
        },
        action: {
            type: String,
            default: 'check',
            validator: (value) => ['add', 'edit', 'check'].includes(value)
        },
        visibleFields: {
            type: Object,
            default: () => ({
                add: [],
                edit: [],
                check: []
            })
        },
        labelWidth: {
            type: String,
            default: '180px'
        },
        showFooter: {
            type: Boolean,
            default: true
        }
    },
    computed: {
        modeText() {
            const map = new Map([
                ['add', '新增'],
                ['edit', '修改'],
                ['check', '查看']
            ]);
            return map.get(this.action);
        },

        // 检查模式下禁用验证规则
        checkRules() {
            return this.action === 'check' ? {} : this.rules;
        },

        // 处理后的表单配置
        processedFormConfig() {
            return this.formConfig.map((item) => {
                // 查看模式下使用formatter格式化显示值
                if (this.action === 'check' && item.type === 'select' && item.options) {
                    return {
                        ...item,
                        formatter:
                            item.formatter ||
                            ((value) => {
                                const option = item.options.find((opt) => opt.value === value);
                                return option ? option.label : value;
                            })
                    };
                }
                return item;
            });
        }
    },
    methods: {
        getComponentType(type) {
            return COMPONENT_MAP[type] || type;
        },

        getPlaceholder(item) {
            return item.placeholder || `请输入${item.label}`;
        },

        shouldShow(prop) {
            if (!prop) return false;
            return this.visibleFields[this.action].includes(prop);
        },

        // 获取查看模式下显示的值
        getDisplayValue(item) {
            if (item.formatter) {
                return item.formatter(this.formData[item.prop]);
            }
            return this.formData[item.prop] || this.formData[item.prop] == 0
                ? this.formData[item.prop]
                : '-';
        },

        submitForm() {
            this.$refs.form.validate((valid) => {
                if (valid) {
                    this.$emit('submit', this.formData, this.modeText);
                }
            });
        },

        validateForm() {
            return this.$refs.form.validate();
        },

        resetForm() {
            this.$refs.form.resetFields();
        }
    }
};
</script>

<style lang="less" scoped>
@import '../../common.less';
.base-form {
    width: 60%;
    height: 100%;
    margin-top: 24px;

    .form-footer {
        padding-bottom: 22px;

        .form-footer-btn {
            margin-left: 180px;
        }
    }
}
.el-select {
    width: 100%;
}
</style>
