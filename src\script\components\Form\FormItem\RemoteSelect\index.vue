<template>
    <el-select
        :style="[$attrs.itemStyle || {}]"
        v-model.trim="currentSelect"
        filterable
        remote
        @change="changeSelect"
        @focus="focusTarget"
        :remote-method="remoteMethod"
        :class="addClass"
        v-bind="$attrs"
        v-on="$listeners"
    >
        <template slot="prefix">
            <div style="padding-left: 5px">
                <i class="el-icon-search"></i>
            </div>
        </template>
        <template>
            <el-option
                v-for="(item, i) of options"
                :label="item.label"
                :key="i"
                :value="item.value"
                v-bind="item"
            >
            </el-option>
        </template>
    </el-select>
</template>

<script>
export default {
    name: 'RamsReSelect',
    model: {
        prop: 'value',
        event: 'change'
    },
    props: {
        value: {
            type: [String, Number, Boolean],
            default: () => ''
        },
        addClass: {
            type: [Object, Array, String]
        },
        slotNames: {
            type: Array
        },
        options: {
            type: Array,
            default: () => []
        }
    },
    data() {
        return {
            currentSelect: this.value
        };
    },

    watch: {
        value(newVal) {
            this.currentSelect = newVal;
        }
    },

    mounted() {},

    methods: {
        changeSelect(newVale) {
            this.$emit('change', newVale);
        },
        focusTarget() {
            this.$emit('focusTarget');
        },
        remoteMethod(query) {
            this.$emit('remoteMethod', query);
        }
    }
};
</script>
<style lang="less" scoped>
/deep/ .el-icon-search {
    color: #409eff;
}
</style>
