<template>
    <div class="page-container">
        <Header :nav="navOptions" @goBack="goBack" />
        <div class="main-content">
            <tableContent
                ref="tableContent"
                @action="handleAction"
                v-show="['table'].includes(nowAction)"
            />
            <detailContent
                :row="row"
                :action="nowAction"
                @goBack="goBack"
                v-if="['edit'].includes(nowAction)"
            />
            <checkContent :row="row" v-else-if="['check'].includes(nowAction)" />
            <ImportDialog
                :contentVisible.sync="importDialogVisible"
                @confirm="confirmImport"
                @download="downloadTemplate"
                :loading="loading"
            />
        </div>
    </div>
</template>

<script>
import Header from '../components/Header/index.vue';
import tableContent from './components/tableContent/index.vue';
import { handleDownloadFile } from '@/script/utils/method';

export default {
    name: 'HighwayCommunityRelationManager',
    components: {
        Header,
        tableContent,
        detailContent: () => import('./components/detailContent/index.vue'),
        checkContent: () => import('./components/checkContent/index.vue'),
        ImportDialog: () => import('../components/importDialog/index.vue')
    },
    data() {
        return {
            navOptions: {
                showBack: true,
                beforePathName: '',
                lastPathName: '高速道路小区关系指标管理'
            },
            nowAction: 'table', // 当前页面状态，table: 表格页面，add: 添加页面，edit: 编辑页面，check: 查看页面
            row: {},
            importDialogVisible: false,
            loading: false
        };
    },
    watch: {
        nowAction: {
            handler(newVal) {
                if (!this.$route.query.gdb_appId && this.nowAction === 'table') {
                    this.navOptions.showBack = false;
                }
            },
            immediate: true
        }
    },
    methods: {
        goBack() {
            // 页面是由展板点击进入且当前页面是表格页面时，返回按钮返回展板页面
            if (this.$route.query.gdb_appId && this.nowAction === 'table') {
                if (this.$route.query.appId) {
                    // 展板页面是被嵌套的，则返回展板页的父页面
                    frameService.appOpenById(this.$route.query.appId, {
                        sd_appId: this.$route.query.gdb_appId
                    });
                } else {
                    // 展板页面是独立页面，则返回展板页面
                    frameService.appOpenById(this.$route.query.gdb_appId);
                }
                return;
            }
            this.updateNavigation({
                ...this.$options.data().navOptions,
                action: 'table'
            });
        },
        updateNavigation({ showBack, beforePathName, lastPathName, action }) {
            this.navOptions.showBack = showBack;
            this.navOptions.beforePathName = beforePathName;
            this.navOptions.lastPathName = lastPathName;
            this.nowAction = action;
            this.nowAction === 'table' && this.$refs.tableContent.refreshTable();
        },
        handleAction(action, row = {}) {
            // 导入时不走这里了
            if (action === 'add') {
                this.importDialogVisible = true;
                return;
            }
            this.row = row;
            const actionConfig = {
                add: {
                    lastPathName: '道路小区新增'
                },
                edit: {
                    lastPathName: '道路小区修改'
                },
                check: {
                    lastPathName: row.hasOwnProperty('roadName') ? row.roadName : '道路小区详情'
                }
            };
            // 目前只考虑两级深度路径路由
            if (action in actionConfig) {
                this.updateNavigation({
                    showBack: true,
                    beforePathName: `${this.$options.data().navOptions.lastPathName} /`,
                    lastPathName: actionConfig[action].lastPathName,
                    action
                });
            }
        },
        confirmImport(param) {
            let file = param.file;
            let params = new FormData();
            params.append('file', file);
            $getFileRequst(
                'post',
                'mtexapi/region-service/regionsubscribe/highSpeedEciDetails/importECIData',
                params,
                {
                    headers: {
                        'content-type': 'multipart/form-data'
                    }
                }
            )
                .then(({ serviceFlag, returnMsg }) => {
                    if (serviceFlag === 'TRUE') {
                        this.$message.success('导入成功');
                        this.$refs.tableContent.refreshTable();
                    } else {
                        this.$message.error(returnMsg);
                    }
                })
                .catch((err) => {
                    console.log(err);
                    this.$message.error('导入失败');
                });
        },
        downloadTemplate() {
            this.loading = true;
            $getFileRequst(
                'post',
                'mtexapi/region-service/regionsubscribe/highWayDetails/download',
                {
                    fileName: '高速道路小区管理导入模板' //高速道路小区管理导入模板、高速道路管理导入模板
                },
                {
                    responseType: 'blob'
                }
            )
                .then((res) => {
                    handleDownloadFile(
                        res,
                        (err) => {
                            this.$message(err);
                        },
                        '高速道路小区管理导入模板'
                    );
                })
                .catch(() => {
                    this.$message('下载失败');
                })
                .finally(() => {
                    this.loading = false;
                });
        }
    }
};
</script>

<style lang="less" scoped>
@import '../common.less';
.page-container {
    width: 100%;
    height: 100%;
    display: flex;
    flex-direction: column;
    background: linear-gradient(180deg, #f2f9fe 0%, #f8fafe 100%);
    padding: 30px 50px 10px 50px;
    overflow: auto;
}
.main-content {
    flex: 1;
    height: 0;
}
</style>
