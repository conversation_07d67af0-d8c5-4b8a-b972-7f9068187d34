<template>
    <commonTableContent
        ref="metroCrawlerListTable"
        :fields="fields"
        :initialForm="initialForm"
        addButtonText="新增"
        :columns="columns"
        :data="tableData"
        :dataTotal="total"
        @get-table-data="handleGetTableData"
        @action="handleAction"
        @delete="handleDelete"
    >
        <!-- 自定义插槽 -->
        <template #cityCode="{ row }">
            <span>{{ getLabelByValue(SHANDONG_CITY, row.cityCode) }}</span>
        </template>
        <template #scrapeObject="{ row }">
            <span>{{ row.scrapeObject }}</span>
        </template>
        <template #taskStatus="{ row }">
            <span>{{ getLabelByValue(TASK_STATUS, row.taskStatus) }}</span>
        </template>
        <template #operation="{ row }">
            <el-button class="p-0" type="text" @click="handleDelete(row)">删除</el-button>
            <el-button class="p-0" type="text" @click="reload(row)">重启</el-button>
            <el-button class="p-0" type="text" @click="handleAction('edit', row)">编辑</el-button>
        </template>
    </commonTableContent>
</template>

<script>
import commonTableContent from '../../../components/commonTableContent/index.vue';
import {
    formCols,
    tableCols,
    SCRAPE_OBJECT,
    TASK_STATUS
} from '@/script/constant/cityRoadNetwork/metroCrawlerManager.js';
import { getLabelByValue } from '@/script/utils/method.js';
import { SHANDONG_CITY } from '@/script/constant/shandong';

export default {
    name: 'table-content',
    components: {
        commonTableContent
    },
    data() {
        return {
            initialForm: {
                scrapeObject: undefined,
                cityCode: ''
            },
            total: 0,
            tableData: [],
            getLabelByValue
        };
    },
    computed: {
        fields() {
            return formCols;
        },
        columns() {
            return tableCols;
        },
        SHANDONG_CITY() {
            return SHANDONG_CITY();
        },
        SCRAPE_OBJECT() {
            return SCRAPE_OBJECT;
        },
        TASK_STATUS() {
            return TASK_STATUS;
        }
    },
    methods: {
        async handleGetTableData(params, pagination) {
            const [startTime, endTime] = params.timeRange || [];
            const payload = {
                startTime,
                endTime,
                scrapeObject: params.scrapeObject,
                cityCode: params.cityCode,
                pageNo: pagination.curPage,
                pageSize: pagination.pageSize
            };

            let { data } = await $request('post', 'mtexapi/region-service/card/task/list', payload);
            this.tableData = data.data;
            this.total = data.total;
        },
        handleAction(action, row = {}) {
            // 将action转发到父组件
            this.$emit('action', action, row);
        },
        handleDelete(row) {
            this.$confirm('确定要删除该任务吗？', '提示', {
                confirmButtonText: '确定',
                cancelButtonText: '取消',
                type: 'warning'
            })
                .then(() => {
                    $request('post', 'mtexapi/region-service/card/task/delete', {
                        id: row.id
                    })
                        .then(({ serviceFlag, returnMsg }) => {
                            if (serviceFlag === 'TRUE') {
                                this.refreshTable();
                                this.$message({
                                    type: 'success',
                                    message: '删除成功'
                                });
                            } else {
                                this.$message({
                                    type: 'error',
                                    message: returnMsg
                                });
                            }
                        })
                        .catch(() => {});
                })
                .catch(() => {
                    // 用户取消删除
                });
        },
        refreshTable() {
            this.$refs.metroCrawlerListTable.search();
        },
        reload(row) {
            this.$confirm('确定要重启该任务吗？', '提示', {
                confirmButtonText: '确定',
                cancelButtonText: '取消',
                type: 'warning'
            })
                .then(() => {
                    $request('post', 'mtexapi/region-service/card/task/restart', {
                        id: row.id
                    })
                        .then(({ serviceFlag, returnMsg }) => {
                            if (serviceFlag === 'TRUE') {
                                this.refreshTable();
                                this.$message({
                                    type: 'success',
                                    message: returnMsg
                                });
                            } else {
                                this.$message({
                                    type: 'error',
                                    message: returnMsg
                                });
                            }
                        })
                        .catch(() => {});
                })
                .catch(() => {
                    // 用户取消删除
                });
        }
    },
    mounted() {
        this.refreshTable();
    }
};
</script>
