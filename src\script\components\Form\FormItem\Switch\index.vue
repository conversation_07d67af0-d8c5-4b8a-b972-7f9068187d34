<template>
    <el-switch v-bind="$attrs" v-on="$listeners" :class="[addClass]" :style="itemStyle">
    </el-switch>
</template>

<script>
export default {
    name: 'RamsSwitch',
    props: {
        itemStyle: {
            type: Object
        },
        addClass: {
            type: [Object, Array, String]
        },
        /*
            开启loading后 需要调用者 自行获取按钮实例关闭loading属性
        */
        needLoading: {
            type: Boolean,
            default: false
        }
    },
    data() {
        return {
            loading: false
        };
    }
};
</script>
<style lang="less" scoped></style>
