<template>
    <div class="content">
        <Card title="站点基础属性">
            <div class="form-wrapper">
                <BaseForm
                    ref="baseForm"
                    :form-data="formData"
                    :form-config="formConfig"
                    :rules="rules"
                    :action="action"
                    :visible-fields="nowFields"
                    @submit="handleSubmit"
                />
            </div>
        </Card>
    </div>
</template>

<script>
import Card from '../../../components/Card/index.vue';
import BaseForm from '../../../components/BaseForm/index.vue';
import { getLabelByValue } from '@/script/utils/method.js';
import { SHANDONG_CITY, SHANDONG_DISTRICT } from '@/script/constant/shandong';
import { COORS_TYPE } from '@/script/constant/cityRoadNetwork/stationInfoManager';

export default {
    name: 'add-or-edit-content',
    components: {
        Card,
        BaseForm
    },
    props: {
        row: {
            type: Object,
            default: () => ({})
        },
        action: {
            type: String,
            default: 'check'
        }
    },
    data() {
        return {
            districtOptions: [],
            formData: {
                id: '',
                regionName: '',
                city: '',
                district: '',
                regionCoors: '',
                coorsType: '',
                highSpeed: 0,
                interCity: 0,
                moving: 0,
                regular: 0
            },
            nowFields: {
                add: [
                    'regionName',
                    'regionCoors',
                    'coorsType',
                    'highSpeed',
                    'interCity',
                    'moving',
                    'regular'
                ],
                edit: [
                    'regionName',
                    'regionCoors',
                    'coorsType',
                    'highSpeed',
                    'interCity',
                    'moving',
                    'regular'
                ],
                check: ['regionName', 'city', 'district']
            },
            rules: {
                regionName: [{ required: true, message: '请输入站点名称', trigger: 'blur' }],
                city: [{ required: true, message: '请选择归属城市', trigger: 'change' }],
                district: [{ required: true, message: '请选择归属区县', trigger: 'change' }],
                regionCoors: [{ required: true, message: '请输入区域轮廓', trigger: 'blur' }],
                coorsType: [{ required: true, message: '请选择坐标系类型', trigger: 'change' }],
                highSpeed: [{ required: true, message: '请选择是否高铁站', trigger: 'change' }],
                interCity: [{ required: true, message: '请选择是否城际站', trigger: 'change' }],
                moving: [{ required: true, message: '请选择是否动车站', trigger: 'change' }],
                regular: [{ required: true, message: '请选择是否普铁站', trigger: 'change' }]
            }
        };
    },
    computed: {
        modeText() {
            const map = new Map([
                ['add', '新增'],
                ['edit', '修改'],
                ['check', '查看']
            ]);
            return map.get(this.action);
        },
        formConfig() {
            const yesNoOptions = [
                { label: '是', value: 1 },
                { label: '否', value: 0 }
            ];
            return [
                {
                    prop: 'regionName',
                    label: '站点名称：',
                    type: 'input',
                    placeholder: '请输入站点名称',
                    attrs: {
                        clearable: true
                    }
                },
                {
                    prop: 'city',
                    label: '归属城市：',
                    type: 'el-select',
                    placeholder: '请选择归属城市',
                    options: SHANDONG_CITY(),
                    attrs: {
                        clearable: true,
                        'popper-class': 'gdb-select-dropdown-dark gdb-popover-dark'
                    },
                    formatter: (value) => getLabelByValue(SHANDONG_CITY(), value)
                },
                {
                    prop: 'district',
                    label: '归属区县：',
                    type: 'el-select',
                    placeholder: '请选择归属区县',
                    options: this.districtOptions,
                    attrs: {
                        clearable: true,
                        filterable: true,
                        'popper-class': 'gdb-select-dropdown-dark gdb-popover-dark'
                    },
                    formatter: (value) => getLabelByValue(SHANDONG_DISTRICT(), value)
                },
                {
                    prop: 'regionCoors',
                    label: '区域轮廓：',
                    type: 'input',
                    placeholder: '请输入区域轮廓经纬度',
                    attrs: {
                        clearable: true,
                        type: 'textarea',
                        rows: 4
                    }
                },
                {
                    prop: 'coorsType',
                    label: '坐标系类型：',
                    type: 'el-select',
                    placeholder: '请选择坐标系类型',
                    options: COORS_TYPE,
                    attrs: {
                        clearable: true,
                        'popper-class': 'gdb-select-dropdown-dark gdb-popover-dark'
                    },
                    formatter: (value) => getLabelByValue(COORS_TYPE, value)
                },
                {
                    prop: 'highSpeed',
                    label: '是否高铁站：',
                    type: 'el-select',
                    placeholder: '是否高铁站',
                    options: yesNoOptions,
                    attrs: {
                        clearable: true,
                        'popper-class': 'gdb-select-dropdown-dark gdb-popover-dark'
                    },
                    formatter: (value) => getLabelByValue(yesNoOptions, value)
                },
                {
                    prop: 'interCity',
                    label: '是否城际站：',
                    type: 'el-select',
                    placeholder: '是否城际站',
                    options: yesNoOptions,
                    attrs: {
                        clearable: true,
                        'popper-class': 'gdb-select-dropdown-dark gdb-popover-dark'
                    },
                    formatter: (value) => getLabelByValue(yesNoOptions, value)
                },
                {
                    prop: 'moving',
                    label: '是否动车站：',
                    type: 'el-select',
                    placeholder: '是否动车站',
                    options: yesNoOptions,
                    attrs: {
                        clearable: true,
                        'popper-class': 'gdb-select-dropdown-dark gdb-popover-dark'
                    },
                    formatter: (value) => getLabelByValue(yesNoOptions, value)
                },
                {
                    prop: 'regular',
                    label: '是否普铁站：',
                    type: 'el-select',
                    placeholder: '是否普铁站',
                    options: yesNoOptions,
                    attrs: {
                        clearable: true,
                        'popper-class': 'gdb-select-dropdown-dark gdb-popover-dark'
                    },
                    formatter: (value) => getLabelByValue(yesNoOptions, value)
                }
            ];
        }
    },
    watch: {
        'formData.city': {
            handler(newVal) {
                if (['check'].includes(this.action)) {
                    return;
                }
                this.updateDistrictOptions(newVal);
                // 重置区县选项
                if (
                    this.formData.district &&
                    !this.districtOptions.find(
                        (d) => d.value.toString() === this.formData.district.toString()
                    )
                ) {
                    !['edit'].includes(this.action) && (this.formData.district = '');
                }
            },
            immediate: true
        }
    },
    methods: {
        updateDistrictOptions(cityId) {
            this.districtOptions = SHANDONG_DISTRICT({ cityId });
        },
        handleSubmit(form, modeText) {
            const apiPathMap = new Map([
                ['add', 'management/train/station/add'],
                ['edit', 'management/train/station/update']
            ]);

            const payloadMap = {
                add: {
                    regionName: form.regionName,
                    regionCoors: form.regionCoors,
                    coorsType: form.coorsType,
                    highSpeed: form.highSpeed,
                    interCity: form.interCity,
                    moving: form.moving,
                    regular: form.regular
                },
                edit: {
                    id: this.formData.id,
                    regionName: form.regionName,
                    regionCoors: form.regionCoors,
                    coorsType: form.coorsType,
                    highSpeed: form.highSpeed,
                    interCity: form.interCity,
                    moving: form.moving,
                    regular: form.regular
                }
            };

            $request(
                'post',
                `mtexapi/region-service/${apiPathMap.get(this.action)}`,
                payloadMap[this.action]
            ).then(({ serviceFlag, returnMsg }) => {
                if (serviceFlag === 'TRUE') {
                    this.$emit('goBack');
                    this.$message({
                        type: 'success',
                        message: `${modeText}成功`
                    });
                    return;
                }
                this.$message({
                    type: 'error',
                    message: returnMsg
                });
            });
        }
    },
    created() {
        if (['edit', 'check'].includes(this.action) && this.row) {
            this.formData = {
                ...this.formData,
                ...this.row
            };
        }
    }
};
</script>

<style lang="less" scoped>
.content {
    min-height: 100%;
    height: max-content;
    display: flex;
    flex-direction: column;

    .form-wrapper {
        display: flex;
        justify-content: center;
        align-items: center;
    }
}
</style>
