<template>
    <commonTableContent
        ref="cityGridListTable"
        :fields="fields"
        :initialForm="initialForm"
        addButtonText="新增"
        :columns="columns"
        :data="tableData"
        :dataTotal="total"
        @get-table-data="handleGetTableData"
        @action="handleAction"
        @delete="handleDelete"
    >
    </commonTableContent>
</template>

<script>
import commonTableContent from '../../../components/commonTableContent/index.vue';
import {
    communityFormCols,
    communityTableCols
} from '@/script/constant/cityRoadNetwork/cityGridManager.js';
import { getLabelByValue } from '@/script/utils/method';
import { SHANDONG_CITY } from '@/script/constant/shandong';

export default {
    name: 'table-content',
    components: {
        commonTableContent
    },
    data() {
        return {
            initialForm: {
                cityId: '',
                roadName: ''
            },
            total: 0,
            tableData: []
        };
    },
    computed: {
        fields() {
            return communityFormCols;
        },
        columns() {
            return communityTableCols;
        }
    },
    methods: {
        async handleGetTableData(params, pagination) {
            const payload = {
                roadName: params.roadName, //可选
                cityName: getLabelByValue(SHANDONG_CITY(), params.cityId), //可选
                startTime: params.startTime, //可选
                endTime: params.endTime, //可选
                pageNo: pagination.curPage,
                pageSize: pagination.pageSize
            };

            let { data } = await $request(
                'post',
                'mtexapi/region-service/management/roads/query',
                payload
            );
            this.tableData = data.data;
            this.total = data.total;
        },
        handleAction(action, row = {}) {
            // 将action转发到父组件
            this.$emit('action', action, row);
        },
        handleDelete(row) {
            // 删除逻辑
            this.$confirm('确定要删除吗？', '提示', {
                confirmButtonText: '确定',
                cancelButtonText: '取消',
                type: 'warning'
            })
                .then(() => {
                    $request('post', 'mtexapi/region-service/management/roads/delete', {
                        ids: [row.id]
                    })
                        .then(({ serviceFlag, returnMsg }) => {
                            if (serviceFlag === 'TRUE') {
                                this.refreshTable();
                                this.$message({
                                    type: 'success',
                                    message: '删除成功'
                                });
                            } else {
                                this.$message({
                                    type: 'error',
                                    message: returnMsg
                                });
                            }
                        })
                        .catch(() => {});
                })
                .catch(() => {
                    // 用户取消删除
                });
        },
        refreshTable() {
            this.$refs.cityGridListTable.search();
        }
    },
    mounted() {
        this.refreshTable();
    }
};
</script>
