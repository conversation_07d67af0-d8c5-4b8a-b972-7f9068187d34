import { SHANDONG_CITY } from '@/script/constant/shandong';

// 地铁线路选项配置
const SUBWAY_LINES = [
    { label: '1号线', value: 'line1' },
    { label: '2号线', value: 'line2' },
    { label: '3号线', value: 'line3' },
    { label: '4号线', value: 'line4' },
    { label: '5号线', value: 'line5' },
    { label: '6号线', value: 'line6' },
    { label: '7号线', value: 'line7' },
    { label: '8号线', value: 'line8' },
    { label: '9号线', value: 'line9' },
    { label: '10号线', value: 'line10' },
    { label: '11号线', value: 'line11' },
    { label: '12号线', value: 'line12' },
    { label: '13号线', value: 'line13' },
    { label: '14号线', value: 'line14' },
    { label: '15号线', value: 'line15' },
    { label: '16号线', value: 'line16' },
    { label: '17号线', value: 'line17' },
    { label: '18号线', value: 'line18' }
];

// 覆盖类型选项配置
const COVERAGE_TYPES = [
    { label: '室内', value: 'indoor' },
    { label: '室外', value: 'outdoor' }
];

// 基站类别选项配置
const STATION_CATEGORIES = [
    { label: '4G', value: '4g' },
    { label: '5G', value: '5g' }
];

// 筛选条件配置
const cellSiteFormCols = [
    {
        prop: 'cityId',
        label: '城市名称',
        element: 'el-select',
        attrs: {
            placeholder: '请选择城市',
            clearable: true,
            'popper-class': 'gdb-select-dropdown-dark gdb-popover-dark'
        },
        slot: {
            element: 'el-option',
            enums: SHANDONG_CITY()
        },
        span: 4
    },
    {
        prop: 'subwayLine',
        label: '地铁线路',
        element: 'el-select',
        attrs: {
            placeholder: '请选择地铁线路',
            clearable: true,
            'popper-class': 'gdb-select-dropdown-dark gdb-popover-dark'
        },
        slot: {
            element: 'el-option',
            enums: SUBWAY_LINES
        },
        span: 4
    },
    {
        prop: 'coverageType',
        label: '覆盖类型',
        element: 'el-select',
        attrs: {
            placeholder: '请选择覆盖类型',
            clearable: true,
            'popper-class': 'gdb-select-dropdown-dark gdb-popover-dark'
        },
        slot: {
            element: 'el-option',
            enums: COVERAGE_TYPES
        },
        span: 4
    },
    {
        prop: 'stationCategory',
        label: '基站类别',
        element: 'el-select',
        attrs: {
            placeholder: '请选择基站类别',
            clearable: true,
            'popper-class': 'gdb-select-dropdown-dark gdb-popover-dark'
        },
        slot: {
            element: 'el-option',
            enums: STATION_CATEGORIES
        },
        span: 4
    },
    {
        span: 8
    }
];

// 主列表表格字段配置
const cellSiteTableCols = [
    {
        prop: 'cityName',
        label: '城市名称'
    },
    {
        prop: 'subwayLineName',
        label: '归属线路'
    },
    {
        prop: 'stationName',
        label: '站点名称'
    },
    {
        prop: 'cellEci',
        label: '小区ECI'
    },
    {
        prop: 'coverageType',
        label: '覆盖类型'
    },
    {
        prop: 'stationCategory',
        label: '基站类别'
    },
    {
        prop: 'signalStrength',
        label: '信号强度'
    },
    {
        prop: 'lastUpdateTime',
        label: '最后更新时间',
        'min-width': 180
    },
    {
        prop: 'operation',
        label: '操作',
        width: 120
    }
];

// 详情查看页面表格字段配置
const cellSiteDetailTableCols = [
    {
        prop: 'indexNo',
        label: '序号',
        width: 60
    },
    {
        prop: 'cityName',
        label: '城市',
        width: 100
    },
    {
        prop: 'subwayLineName',
        label: '归属线路',
        width: 120
    },
    {
        prop: 'stationName',
        label: '站点名称',
        width: 150
    },
    {
        prop: 'cellEci',
        label: '小区ECI',
        width: 120
    },
    {
        prop: 'coverageType',
        label: '覆盖类型',
        width: 100
    },
    {
        prop: 'stationCategory',
        label: '基站类别',
        width: 100
    },
    {
        prop: 'signalStrength',
        label: '信号强度',
        width: 100
    },
    {
        prop: 'longitude',
        label: '经度',
        width: 120
    },
    {
        prop: 'latitude',
        label: '纬度',
        width: 120
    },
    {
        prop: 'lastUpdateTime',
        label: '最后更新时间',
        'min-width': 180
    }
];

// 地图可视化配置
const mapVisualizationConfig = {
    // 地铁线路样式配置
    subwayLineStyle: {
        color: 0x0099ff,
        width: 4,
        opacity: 0.8
    },
    // 基站点样式配置
    stationPointStyle: {
        normal: {
            color: 0x00ff00,
            size: 8,
            opacity: 0.8
        },
        selected: {
            color: 0xff6600,
            size: 12,
            opacity: 1.0
        },
        hover: {
            color: 0xffff00,
            size: 10,
            opacity: 0.9
        }
    },
    // 小区覆盖范围样式配置
    cellCoverageStyle: {
        indoor: {
            color: 0x00ff00,
            opacity: 0.3,
            borderColor: 0x00aa00,
            borderWidth: 2
        },
        outdoor: {
            color: 0x0099ff,
            opacity: 0.3,
            borderColor: 0x0066aa,
            borderWidth: 2
        }
    },
    // 信号强度颜色映射
    signalStrengthColors: {
        strong: 0x00ff00, // 绿色 - 强信号
        medium: 0xffff00, // 黄色 - 中等信号
        weak: 0xff6600, // 橙色 - 弱信号
        poor: 0xff0000 // 红色 - 差信号
    }
};

export {
    cellSiteFormCols,
    cellSiteTableCols,
    cellSiteDetailTableCols,
    mapVisualizationConfig,
    SUBWAY_LINES,
    COVERAGE_TYPES,
    STATION_CATEGORIES
};
