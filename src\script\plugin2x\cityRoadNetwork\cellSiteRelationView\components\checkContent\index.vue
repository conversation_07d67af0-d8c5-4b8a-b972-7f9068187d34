<template>
    <div class="content">
        <Card title="基站小区与地铁线路关系可视化展示">
            <div class="content-wrapper">
                <mtv-gis
                    class="home-map"
                    ref="mtvGis"
                    :totaloptions="gistotalOptions"
                    @onLoad="gisOnLoad"
                    :autoActive="false"
                ></mtv-gis>
                <!-- 表格 -->
                <dataTable
                    ref="listTable"
                    class="list-table"
                    :columns="columns"
                    :data="tableData"
                    :total="total"
                    :pagination="pagination"
                    :updateTable="getTableData"
                    @row-click="onRowClick"
                    stripe
                    isHideUpLine
                >
                    <template #indexNo="{ row, inx }">
                        <div>
                            {{ inx + 1 }}
                        </div>
                    </template>
                    <template #lastUpdateTime="{ row }">
                        <div>
                            {{ formatTime(row.lastUpdateTime) }}
                        </div>
                    </template>
                </dataTable>
            </div>
        </Card>
    </div>
</template>

<script>
import Card from '../../../components/Card/index.vue';
import dataTable from '@/script/components/dataTable/index.vue';
import {
    cellSiteDetailTableCols,
    mapVisualizationConfig
} from '@/script/constant/cityRoadNetwork/cellSiteRelationView.js';
import { gistotalOptions, getMayType, changeGisColor } from '@/script/constant/gis.js';
import { formatTime } from '@/script/utils/method.js';

export default {
    name: 'check-content',
    components: {
        Card,
        dataTable
    },
    props: {
        row: {
            type: Object,
            default: () => ({})
        }
    },
    computed: {
        columns() {
            return cellSiteDetailTableCols;
        }
    },
    data() {
        return {
            formatTime,
            gistotalOptions,
            mapVisualizationConfig,
            total: 0,
            pagination: {
                curPage: 1,
                pageSize: 15
            },
            tableData: [],
            selectedCellIndex: -1, // 当前选中的小区索引
            dialogGIS: null,
            subwayLayer: null,
            cellLayer: null
        };
    },
    methods: {
        gisOnLoad() {
            this.dialogGIS = this.$refs.mtvGis.getEntity();
            // 设置底图
            if (getMayType() === 'default') {
                this.dialogGIS.tileLayerList['底图图层'].visible = false;
                //重写getUrl方法
                this.dialogGIS.tileLayerList['高德底图'].getUrl = (x, y, z) => {
                    return `${location.origin}/MapUrl/api/appmaptile?x=${x}&y=${y}&z=${z}`;
                };
            } else {
                this.dialogGIS.tileLayerList['高德底图'] &&
                    (this.dialogGIS.tileLayerList['高德底图'].visible = false);
            }
            changeGisColor(this.dialogGIS);
            this.getTableData();
            this.getSubwayLineInfo();
        },
        async getTableData(paginationData = { ...this.pagination }) {
            // 暂时使用测试数据，等接口提供后替换
            try {
                // 模拟API延迟
                await new Promise((resolve) => setTimeout(resolve, 300));

                // 生成测试数据
                const mockData = this.generateDetailMockData(paginationData.pageSize);

                this.tableData = mockData;
                this.total = 20; // 模拟总数

                // 如果有数据，默认选中第一条记录
                if (this.tableData.length > 0) {
                    this.selectedCellIndex = 0;
                    this.highlightCell(0);
                }
            } catch (error) {
                console.error('获取基站小区详情数据失败:', error);
                this.tableData = [];
                this.total = 0;
            }

            // 真实接口调用代码（暂时注释）
            /*
            let { data } = await $request(
                'post',
                'mtexapi/region-service/management/cellsite/detail/query',
                {
                    cellEci: this.row.cellEci,
                    subwayLine: this.row.subwayLine,
                    pageNo: paginationData.curPage,
                    pageSize: paginationData.pageSize
                }
            );
            this.tableData = data.list || data.data || [];
            this.total = data.total || 0;

            // 如果有数据，默认选中第一条记录
            if (this.tableData.length > 0) {
                this.selectedCellIndex = 0;
                this.highlightCell(0);
            }
            */
        },
        getSubwayLineInfo() {
            // 暂时使用测试数据，等接口提供后替换
            setTimeout(() => {
                const mockLineData = this.generateSubwayLineMockData();
                this.drawSubwayLine(mockLineData);
            }, 200);

            // 真实接口调用代码（暂时注释）
            /*
            $request('post', 'mtexapi/region-service/management/subway/line/detail', {
                subwayLine: this.row.subwayLine,
                cityId: this.row.cityId
            }).then(({ data: res }) => {
                this.drawSubwayLine(res);
            });
            */
        },
        drawSubwayLine(data) {
            if (!data || data.length === 0) {
                return;
            }

            // 清除之前的地铁线路图层
            if (this.subwayLayer) {
                this.dialogGIS.gis.scene.remove(this.subwayLayer.Group);
            }

            // 准备地铁线路数据
            let htStep = 0.0001;
            let lineData = data.map((item) => {
                return {
                    points: [
                        {
                            lat: Number(item.startLatLng.split(',')[0]),
                            lng: Number(item.startLatLng.split(',')[1]),
                            ht: htStep * 0
                        },
                        {
                            lat: Number(item.endLatLng.split(',')[0]),
                            lng: Number(item.endLatLng.split(',')[1]),
                            ht: htStep * 0
                        }
                    ],
                    color: this.mapVisualizationConfig.subwayLineStyle.color
                };
            });

            // 创建地铁线路图层
            this.subwayLayer = new this.dialogGIS.layer();
            this.subwayLayer.visible = true;
            this.dialogGIS.gis.scene.add(this.subwayLayer.Group);

            // 设置线路样式
            lineData.autoScale = true;
            lineData.width = this.mapVisualizationConfig.subwayLineStyle.width;

            // 创建地铁线路模型
            let lineMesh = this.dialogGIS.meshList.road.create(lineData);
            this.subwayLayer.add(lineMesh);

            // 绘制基站小区点位
            this.drawCellSites();

            // 更新GIS
            this.dialogGIS.gis.needUpdate = true;

            // 缩放到合适视角
            const regionCoorsList = data.map((item) => {
                return {
                    lat: Number(item.startLatLng.split(',')[0]),
                    lng: Number(item.startLatLng.split(',')[1])
                };
            });
            this.dialogGIS.cameraControl.zoomByPoints(regionCoorsList, 1.2);
        },
        drawCellSites() {
            // 清除之前的基站小区图层
            if (this.cellLayer) {
                this.dialogGIS.gis.scene.remove(this.cellLayer.Group);
            }

            if (!this.tableData || this.tableData.length === 0) {
                return;
            }

            // 创建基站小区图层
            this.cellLayer = new this.dialogGIS.layer();
            this.cellLayer.visible = true;
            this.dialogGIS.gis.scene.add(this.cellLayer.Group);

            // 绘制每个基站小区点位
            this.tableData.forEach((cell, index) => {
                if (cell.latitude && cell.longitude) {
                    this.drawCellPoint(cell, index);
                }
            });

            this.dialogGIS.gis.needUpdate = true;
        },
        drawCellPoint(cell, index) {
            const isSelected = index === this.selectedCellIndex;
            const pointStyle = isSelected
                ? this.mapVisualizationConfig.stationPointStyle.selected
                : this.mapVisualizationConfig.stationPointStyle.normal;

            // 根据信号强度确定颜色
            let color = this.getSignalStrengthColor(cell.signalStrength);
            if (isSelected) {
                color = pointStyle.color;
            }

            const pointData = {
                lat: Number(cell.latitude),
                lng: Number(cell.longitude),
                ht: 0.001,
                color: color,
                size: pointStyle.size,
                opacity: pointStyle.opacity
            };

            // 创建点位模型（这里需要根据实际的GIS库API调整）
            // 示例代码，具体实现需要根据mtv-gis的API
            const pointMesh = this.dialogGIS.meshList.point?.create?.(pointData);
            if (pointMesh) {
                this.cellLayer.add(pointMesh);
            }
        },
        getSignalStrengthColor(strength) {
            const colors = this.mapVisualizationConfig.signalStrengthColors;
            if (strength >= 80) return colors.strong;
            if (strength >= 60) return colors.medium;
            if (strength >= 40) return colors.weak;
            return colors.poor;
        },
        highlightCell(index) {
            this.selectedCellIndex = index;
            // 重新绘制基站小区点位以更新高亮状态
            this.drawCellSites();
        },
        onRowClick(row) {
            // 找到点击行的索引
            const index = this.tableData.findIndex((item) => item.cellEci === row.cellEci);
            if (index !== -1) {
                this.highlightCell(index);

                // 将地图视角移动到选中的基站小区
                if (row.latitude && row.longitude) {
                    const targetPoint = {
                        lat: Number(row.latitude),
                        lng: Number(row.longitude)
                    };
                    this.dialogGIS.cameraControl.zoomByPoints([targetPoint], 1.5);
                }
            }
        },
        generateDetailMockData(pageSize) {
            const cities = ['济南市', '青岛市', '烟台市'];
            const subwayLines = ['1号线', '2号线', '3号线'];
            const coverageTypes = ['室内', '室外'];
            const stationCategories = ['4G', '5G'];
            const stationNames = ['泉城广场站', '趵突泉站', '五四广场站', '栈桥站', '大明湖站'];

            const mockData = [];
            for (let i = 0; i < pageSize; i++) {
                const cityIndex = Math.floor(Math.random() * cities.length);
                const lineIndex = Math.floor(Math.random() * subwayLines.length);
                const stationIndex = Math.floor(Math.random() * stationNames.length);

                mockData.push({
                    id: `detail_${Date.now()}_${i}`,
                    cityName: cities[cityIndex],
                    subwayLineName: subwayLines[lineIndex],
                    stationName: stationNames[stationIndex],
                    cellEci: `ECI${String(Math.floor(Math.random() * 999999)).padStart(6, '0')}`,
                    coverageType: coverageTypes[Math.floor(Math.random() * coverageTypes.length)],
                    stationCategory:
                        stationCategories[Math.floor(Math.random() * stationCategories.length)],
                    signalStrength: Math.floor(Math.random() * 40) + 60,
                    latitude: (36.65 + Math.random() * 0.1).toFixed(6),
                    longitude: (117.1 + Math.random() * 0.2).toFixed(6),
                    lastUpdateTime: new Date(Date.now() - Math.random() * 7 * 24 * 60 * 60 * 1000)
                        .toISOString()
                        .slice(0, 19)
                        .replace('T', ' ')
                });
            }
            return mockData;
        },
        generateSubwayLineMockData() {
            // 生成模拟的地铁线路数据（济南地铁1号线部分站点）
            const stations = [
                { lat: 36.651216, lng: 117.120092 }, // 工研院站
                { lat: 36.651216, lng: 117.135092 }, // 大学城站
                { lat: 36.651216, lng: 117.150092 }, // 长清湖站
                { lat: 36.661216, lng: 117.165092 }, // 园博园站
                { lat: 36.671216, lng: 117.180092 }, // 大杨庄站
                { lat: 36.681216, lng: 117.195092 }, // 玉符河站
                { lat: 36.691216, lng: 117.210092 } // 济南西站
            ];

            const lineSegments = [];
            for (let i = 0; i < stations.length - 1; i++) {
                lineSegments.push({
                    startLatLng: `${stations[i].lat},${stations[i].lng}`,
                    endLatLng: `${stations[i + 1].lat},${stations[i + 1].lng}`
                });
            }

            return lineSegments;
        }
    }
};
</script>

<style lang="less" scoped>
.content {
    min-height: 100%;
    height: max-content;
    display: flex;
    flex-direction: column;

    .content-wrapper {
        display: flex;
        flex-direction: column;
        justify-content: center;
        align-items: center;
        padding: 16px 24px 24px 24px;

        .home-map {
            border-radius: 2px;
            border: 1px solid rgba(64, 155, 255, 0.3);
            width: 100%;
            height: 500px;
            margin-bottom: 16px;
        }

        .list-table {
            width: 100%;
            height: 500px;
        }
    }
}
</style>
