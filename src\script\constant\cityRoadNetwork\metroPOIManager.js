const stationTypes = [
    { label: '普通站', value: '0' },
    { label: '换乘站', value: '1' },
    { label: '始发站', value: '2' },
    { label: '终到站', value: '3' }
];

const metroLines = [
    { label: '1号线', value: '0' },
    { label: '2号线', value: '1' },
    { label: '3号线', value: '2' },
    { label: '4号线', value: '3' },
    { label: '5号线', value: '4' },
    { label: '6号线', value: '5' }
];

const siteFormCols = [
    {
        prop: 'siteName',
        label: '站点名称',
        element: 'el-input',
        attrs: {
            placeholder: '请输入',
            clearable: true
        },
        span: 4
    },
    {
        prop: 'updateTime',
        label: '更新时间',
        element: 'el-date-picker',
        attrs: {
            clearable: true,
            type: 'daterange',
            'range-separator': '-',
            'start-placeholder': '开始日期',
            'end-placeholder': '结束日期',
            format: 'yyyy-MM-dd',
            'value-format': 'yyyy-MM-dd',
            'popper-class': 'gdb-date-picker-dark gdb-popover-dark'
        },
        span: 6
    },
    {
        prop: 'siteType',
        label: '站点分类',
        element: 'el-select',
        attrs: {
            placeholder: '请选择',
            clearable: true,
            'popper-class': 'gdb-select-dropdown-dark gdb-popover-dark'
        },
        slot: {
            element: 'el-option',
            enums: stationTypes
        },
        span: 4
    },
    {
        prop: 'belongLine',
        label: '地铁线路',
        element: 'el-select',
        attrs: {
            placeholder: '请选择',
            clearable: true,
            'popper-class': 'gdb-select-dropdown-dark gdb-popover-dark'
        },
        slot: {
            element: 'el-option',
            enums: metroLines
        },
        span: 4
    },
    {
        span: 6
    }
];

const siteTableCols = [
    {
        prop: 'stationName',
        label: '站点名称'
    },
    {
        prop: 'stationType',
        label: '站点分类'
    },
    {
        prop: 'belongLine',
        label: '归属地铁路线'
    },
    {
        prop: 'stationTags',
        label: '站点标签'
    },
    {
        prop: 'metroPOI',
        label: '地铁站点主POI坐标',
        'min-width': 180
    },
    {
        prop: 'lastUpdateTime',
        label: '最后更新时间',
        'min-width': 180
    },
    {
        prop: 'lastUpdateUser',
        label: '最后更新用户'
    },
    {
        prop: 'operation',
        label: '操作',
        width: 180
    }
];

export { siteFormCols, siteTableCols, stationTypes, metroLines };
