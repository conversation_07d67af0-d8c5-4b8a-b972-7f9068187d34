<template>
    <el-checkbox :style="[$attrs.itemStyle || {}]" v-model="checked" @change="changeSelect">
        {{ options.label }}
    </el-checkbox>
</template>

<script>
export default {
    name: 'RamsCheckBox',
    model: {
        prop: 'value',
        event: 'change'
    },
    props: {
        value: {
            type: Boolean,
            default: false
        },
        options: {
            type: Object,
            default: () => {}
        }
    },
    data() {
        return {
            checked: this.value
        };
    },

    watch: {
        value(newVal) {
            this.checked = newVal;
        }
    },

    methods: {
        changeSelect(newVale) {
            this.$emit('change', newVale);
        }
    }
};
</script>
<style lang="less" scoped></style>
