<template>
    <div class="content">
        <Card title="高铁站周边小区">
            <div class="form-wrapper">
                <BaseForm
                    ref="baseForm"
                    :form-data="formData"
                    :form-config="formConfig"
                    :rules="rules"
                    :action="action"
                    :visible-fields="nowFields"
                    @submit="handleSubmit"
                />
            </div>
        </Card>
    </div>
</template>

<script>
import Card from '../../../components/Card/index.vue';
import BaseForm from '../../../components/BaseForm/index.vue';
import { ruleValidatorWGS84, getLabelByValue } from '@/script/utils/method.js';

export default {
    name: 'detail-content',
    components: {
        Card,
        BaseForm
    },
    props: {
        row: {
            type: Object,
            default: () => ({})
        },
        action: {
            type: String,
            default: 'check'
        }
    },
    data() {
        return {
            nowFields: {
                add: ['trainStationId', 'lacCell', 'centerPoint'],
                edit: ['trainStationName'],
                check: []
            },
            formData: {
                trainStationId: '',
                trainStationName: '',
                lacCell: '',
                centerPoint: ''
            },
            formConfig: [
                {
                    prop: 'trainStationId',
                    label: '站点名称：',
                    type: 'el-select',
                    placeholder: '请选择站点名称',
                    options: (this.row && this.row.stationOptions) || [],
                    attrs: {
                        clearable: true,
                        filterable: true,
                        'popper-class': 'gdb-select-dropdown-dark gdb-popover-dark'
                    },
                    formatter: (value) =>
                        getLabelByValue((this.row && this.row.stationOptions) || [], value)
                },
                {
                    prop: 'trainStationName',
                    label: '站点名称：',
                    type: 'input',
                    placeholder: '请输入站点名称',
                    attrs: {
                        clearable: true
                    }
                },
                {
                    prop: 'lacCell',
                    label: '小区ECI：',
                    type: 'input',
                    placeholder: '请输入小区ECI',
                    attrs: {
                        clearable: true
                    }
                },
                {
                    prop: 'centerPoint',
                    label: '中心经纬度：',
                    type: 'input',
                    placeholder: '请输入中心经纬度（格式：经度,纬度）',
                    attrs: {
                        clearable: true
                    }
                }
            ],
            rules: {
                trainStationId: [{ required: true, message: '请选择站点名称', trigger: 'change' }],
                trainStationName: [{ required: true, message: '请输入站点名称', trigger: 'blur' }],
                lacCell: [{ required: true, message: '请输入小区ECI', trigger: 'blur' }],
                centerPoint: [
                    { required: true, message: '请输入中心经纬度', trigger: 'blur' },
                    {
                        validator: ruleValidatorWGS84,
                        trigger: 'blur'
                    }
                ]
            }
        };
    },
    created() {
        if (['edit', 'check'].includes(this.action) && this.row) {
            this.formData = { ...this.formData, ...this.row };
        }
    },
    methods: {
        handleSubmit(form, modeText) {
            const apiPathMap = new Map([
                ['add', 'station/trainStationLaccell/add'],
                ['edit', 'station/trainStationLaccell/update']
            ]);

            const payload = {
                add: {
                    trainStationId: form.trainStationId,
                    trainStationName: getLabelByValue(this.row.stationOptions, form.trainStationId),
                    lacCell: form.lacCell,
                    longitude: form.centerPoint.split(',')[0],
                    latitude: form.centerPoint.split(',')[1]
                },
                edit: {
                    trainStationId: this.row.trainStationId,
                    trainStationName: form.trainStationName
                }
            };

            $request(
                'post',
                `mtexapi/region-service/${apiPathMap.get(this.action)}`,
                payload[this.action]
            ).then(({ serviceFlag, returnMsg }) => {
                if (serviceFlag === 'TRUE') {
                    this.$emit('goBack');
                    this.$message({
                        type: 'success',
                        message: `${modeText}成功`
                    });
                    return;
                }
                this.$message({
                    type: 'error',
                    message: returnMsg
                });
            });
        }
    }
};
</script>

<style lang="less" scoped>
.content {
    min-height: 100%;
    height: max-content;
    display: flex;
    flex-direction: column;

    .form-wrapper {
        display: flex;
        justify-content: center;
        align-items: center;
    }
}
</style>
