import { SHANDONG_CITY } from '@/script/constant/shandong';

// 1-WGS84坐标系 3-GCJ02火星坐标系 4-BD09百度坐标系
const COORS_TYPE = [
    {
        label: 'WGS84坐标系',
        value: '1'
    },
    {
        label: 'GCJ02火星坐标系',
        value: '3'
    },
    {
        label: 'BD09百度坐标系',
        value: '4'
    }
];

const stationFormCols = [
    {
        prop: 'regionName',
        label: '站点名称',
        element: 'el-input',
        attrs: {
            placeholder: '请输入',
            clearable: true
        },
        span: 4
    },
    {
        prop: 'city',
        label: '归属城市',
        element: 'el-select',
        attrs: {
            placeholder: '请选择',
            clearable: true,
            'popper-class': 'gdb-select-dropdown-dark gdb-popover-dark'
        },
        slot: {
            element: 'el-option',
            enums: SHANDONG_CITY()
        },
        span: 4
    },
    {
        span: 16
    }
];

const stationTableCols = [
    {
        prop: 'regionId',
        label: '站点ID'
    },
    {
        prop: 'regionName',
        label: '站点名称'
    },
    {
        prop: 'city',
        label: '归属地市'
    },
    {
        prop: 'district',
        label: '归属区县'
    },
    {
        prop: 'highSpeed',
        label: '是否高铁站'
    },
    {
        prop: 'interCity',
        label: '是否城际站'
    },
    {
        prop: 'moving',
        label: '是否动车站'
    },
    {
        prop: 'regular',
        label: '是否普铁站'
    },
    {
        prop: 'coorsType',
        label: '坐标系类型'
    },
    {
        prop: 'updateTime',
        label: '最后更新时间',
        'min-width': 180
    },
    {
        prop: 'operation',
        label: '操作',
        width: 180
    }
];

export { stationFormCols, stationTableCols, COORS_TYPE };
