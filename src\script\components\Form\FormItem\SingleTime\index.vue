<template>
    <el-date-picker
        :append-to-body="false"
        :style="[$attrs.itemStyle || {}]"
        placeholder="请选择"
        :class="addClass"
        type="datetime"
        :format="format"
        :value-format="valueFormat"
        :picker-options="pickerOptions"
        v-model="currentVal"
        v-bind="$attrs"
        v-on="$listeners"
        @change="changeInput"
    >
    </el-date-picker>
</template>

<script>
export default {
    name: 'RamsSingleTime',
    model: {
        prop: 'value',
        event: 'change'
    },
    props: {
        value: {
            type: [String, Date]
        },
        addClass: {
            type: [Object, Array, String]
        }
    },
    data() {
        return {
            currentVal: this.value,
            format: 'yyyy-MM-dd HH:mm:ss',
            valueFormat: 'yyyy-MM-dd HH:00:00',
            pickerOptions: {
                // format: 'HH:00:00',
                // selectableRange: '00:00:00 - 23:00:00',
                // 设置时间选择步长
                step: {
                    hour: 1,
                    minute: 0,
                    second: 0
                },
                // 禁用分钟选择
                disabledMinutes: () => {
                    return Array.from({ length: 60 }, (_, i) => i);
                },
                // 禁用秒选择
                disabledSeconds: () => {
                    return Array.from({ length: 60 }, (_, i) => i);
                },
                // 添加默认时间
                defaultTime: '00:00:00'
            }
        };
    },

    watch: {
        value: {
            handler(val) {
                if (val) {
                    // 确保初始值也是整点
                    const date = new Date(val);
                    date.setMinutes(0);
                    date.setSeconds(0);
                    date.setMilliseconds(0);
                    this.currentVal = date;
                } else {
                    this.currentVal = val;
                }
            },
            immediate: true
        }
    },

    methods: {
        changeInput(newVal) {
            if (newVal) {
                // 将时间调整为整点
                const date = new Date(newVal);
                date.setMinutes(0);
                date.setSeconds(0);
                date.setMilliseconds(0);
                this.currentVal = date;
            }
            this.$emit('change', this.currentVal);
        }
    }
};
</script>
