const ENCODE_SIGN = 'ENDCODE';
/**
 * 尝试解密，如果框架开启了加密则进行解密
 * @param {*} params
 */
export const tryEncodeParam = (params) => {
    if (appConfig.IsEncode) {
        let webEncodeParam = '';
        if (appConfig.ParamEncodeType == 'aes') {
            webEncodeParam = aesUtil.encryptAES(JSON.stringify(params)) + ENCODE_SIGN;
        } else {
            let mtCoder = new MTB64Coder();
            webEncodeParam = mtCoder.encodeM2(JSON.stringify(params)) + ENCODE_SIGN;
        }
        params = {
            webEncodeParam: webEncodeParam
        };
    }
    return params;
};
/**
 * 如果框架开启了加密，返回解密后的对象
 * @param {*} result
 * @return {Object} 返回解密后的对象
 */
export const decodeResultUser = (result) => {
    if (result.encodeResp && result.encodeResp.endsWith(ENCODE_SIGN)) {
        try {
            let encodeSorce = result.encodeResp.substring(
                0,
                result.encodeResp.lastIndexOf(ENCODE_SIGN)
            );
            let encodeSt = '';
            if (appConfig.ParamEncodeType === 'aes') {
                encodeSt = aesUtil.decryptAES(encodeSorce);
            } else {
                encodeSt = new MTB64Coder().decodeM2(encodeSorce);
            }
            encodeSt = encodeSt.replace(/\"resource\":(\d+)/g, '"resource": "$1"');
            // return encodeSt;
            return JSON.parse(encodeSt);
        } catch (e) {
            systemUtil.popupMessage(
                '提示',
                '返回数据解密失败',
                'url: ' + systemUtil.rootPath.replace('/mtex', '')
            );
            return null;
        }
    }
    return result;
};
export default {
    tryEncodeParam,
    decodeResultUser
};
