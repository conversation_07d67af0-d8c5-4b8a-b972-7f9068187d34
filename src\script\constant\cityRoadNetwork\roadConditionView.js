import { SHANDONG_CITY } from '@/script/constant/shandong';

const roadConditionFormCols = [
    {
        prop: 'cityId',
        label: '城市名称',
        element: 'el-select',
        attrs: {
            placeholder: '请选择城市',
            clearable: true,
            'popper-class': 'gdb-select-dropdown-dark gdb-popover-dark'
        },
        slot: {
            element: 'el-option',
            enums: SHANDONG_CITY()
        },
        span: 4
    },
    {
        prop: 'roadName',
        label: '道路名称',
        element: 'el-input',
        attrs: {
            placeholder: '请输入道路名称',
            clearable: true
        },
        span: 4
    },
    {
        prop: 'analysisTime',
        label: '分析时间',
        element: 'el-date-picker',
        attrs: {
            clearable: true,
            type: 'date',
            placeholder: '选择时间',
            format: 'yyyy-MM-dd',
            'value-format': 'yyyy-MM-dd',
            'popper-class': 'gdb-date-picker-dark gdb-popover-dark'
        },
        span: 4
    },
    {
        span: 12
    }
];

const roadConditionTableCols = [
    {
        prop: 'roadName',
        label: '道路名称'
    },
    {
        prop: 'analysisTime',
        label: '分析时段'
    },
    {
        prop: 'personFlow',
        label: '人流量'
    },
    {
        prop: 'roadCondition',
        label: '路况'
    }
];

export { roadConditionFormCols, roadConditionTableCols };
