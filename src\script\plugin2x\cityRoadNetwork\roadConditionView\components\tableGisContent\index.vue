<template>
    <div class="table-gis-content mtex-general-display-board-dark-theme">
        <div class="search-container">
            <searchBar :fields="fields" :form="form">
                <div class="tools">
                    <el-button type="primary" size="mini" @click="search(1)">查询</el-button>
                </div>
            </searchBar>
        </div>
        <div class="content-grid">
            <!-- 左侧表格区域 -->
            <div class="table-section">
                <dataTable
                    ref="listTable"
                    class="list-table"
                    :columns="columns"
                    :data="tableData"
                    :total="total"
                    :layout="'total, prev, pager, next, sizes, jumper'"
                    :pagination="pagination"
                    :updateTable="getTableData"
                    stripe
                    highlight-current-row
                    isHideUpLine
                    @row-click="onRowClick"
                >
                </dataTable>
            </div>
            <!-- 右侧地图区域 -->
            <div class="gis-section">
                <mtv-gis
                    class="road-condition-map"
                    ref="mtvGis"
                    :totaloptions="gistotalOptions"
                    @onLoad="gisOnLoad"
                    :autoActive="false"
                ></mtv-gis>
                <!-- 时间轴组件 -->
                <roadConditionTimeline
                    ref="timeline"
                    :initialHour="currentHour"
                    :interval="2000"
                    :pauseDuration="2000"
                    :autoStart="false"
                    @hour-change="onHourChange"
                />
                <!-- 图例 -->
                <div class="road-condition-legend">
                    <p>图例</p>
                    <div class="legend-content">
                        <div class="legend-item" v-for="item in legendData" :key="item.name">
                            <span class="legend-color" :style="{ '--color': item.color }"></span>
                            <span class="legend-name">{{ item.name }}</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</template>

<script>
import searchBar from '../../../components/searchBar/index.vue';
import dataTable from '@/script/components/dataTable/index.vue';
import roadConditionTimeline from '../roadConditionTimeline/index.vue';
import { gistotalOptions, getMayType, changeGisColor } from '@/script/constant/gis.js';
import {
    roadConditionFormCols,
    roadConditionTableCols
} from '@/script/constant/cityRoadNetwork/roadConditionView.js';
import dayjs from 'dayjs';

const msgBg = require('@/img/cityRoadNet/msg-bg.png');

export default {
    name: 'table-gis-content',
    components: {
        searchBar,
        dataTable,
        roadConditionTimeline
    },
    data() {
        return {
            form: {
                roadName: undefined,
                cityId: '',
                analysisTime: undefined
            },
            total: 0,
            tableData: [],
            pagination: {
                curPage: 1,
                pageSize: 15
            },
            gistotalOptions,
            dialogGIS: null,
            currentHour: 1,
            selectedRoadData: null,
            roadLayer: null,
            msgDivLayer: null,
            legendData: [
                { name: '畅通', color: '#25D725' },
                { name: '基本畅通', color: '#C4FF4E' },
                { name: '拥堵', color: '#D7A225' },
                { name: '严重拥堵', color: '#EE3E23' }
            ]
        };
    },
    computed: {
        fields() {
            return roadConditionFormCols;
        },
        columns() {
            return roadConditionTableCols;
        }
    },
    methods: {
        search(pageIndex = 1) {
            this.pagination.curPage = pageIndex;
            this.$refs.listTable.scrollToTop();
            this.getTableData(this.pagination);
        },
        getTableData(paginationData = {}) {
            this.handleGetTableData(this.form, paginationData);
        },
        async handleGetTableData(_, pagination) {
            // const payload = {
            //     roadName: params.roadName,
            //     cityId: params.cityId,
            //     analysisTime: params.analysisTime,
            //     pageNo: pagination.curPage,
            //     pageSize: pagination.pageSize
            // };

            // 实际项目中需要替换为真实的API
            // let { data } = await $request(
            //     'post',
            //     'mtexapi/region-service/management/road/condition/query',
            //     payload
            // );
            // 模拟数据
            function generateTestData(count = 5) {
                const testData = [];

                // 初始化第一个起点
                let [lat, lng] = [36.6507 + Math.random() * 0.1, 117.114 + Math.random() * 0.1];

                for (let i = 0; i < count; i++) {
                    const nextLat = 36.6507 + Math.random() * 0.1;
                    const nextLng = 117.114 + Math.random() * 0.1;

                    testData.push({
                        startLatLng: `${lat},${lng}`,
                        endLatLng: `${nextLat},${nextLng}`
                    });

                    [lat, lng] = [nextLat, nextLng]; // 更新坐标
                }

                return testData;
            }
            const data = {
                data: Array.from({ length: 96 }, (_, i) => ({
                    id: i + 1,
                    roadName: '济南路' + (i + 1),
                    analysisTime: dayjs(new Date()).format('YYYY-MM-DD'),
                    personFlow: Math.floor(Math.random() * 1000),
                    roadCondition: '畅通',
                    // 模拟道路坐标数据
                    roadSegments: generateTestData(3)
                })).slice(
                    (pagination.curPage - 1) * pagination.pageSize,
                    pagination.curPage * pagination.pageSize
                ),
                total: 96
            };
            this.tableData = data.data;
            this.total = data.total;

            // 自动选择第一条数据并绘制道路
            if (data.data.length > 0 && !this.selectedRoadData) {
                this.selectRoadData(data.data[0]);
                this.$refs.listTable.$refs.myTable.setCurrentRow(data.data[0]);
            }
        },
        gisOnLoad() {
            this.dialogGIS = this.$refs.mtvGis.getEntity();
            // 设置底图
            if (getMayType() === 'default') {
                this.dialogGIS.tileLayerList['底图图层'].visible = false;
                //重写getUrl方法
                this.dialogGIS.tileLayerList['高德底图'].getUrl = (x, y, z) => {
                    return `${location.origin}/MapUrl/api/appmaptile?x=${x}&y=${y}&z=${z}`;
                };
            } else {
                this.dialogGIS.tileLayerList['高德底图'] &&
                    (this.dialogGIS.tileLayerList['高德底图'].visible = false);
            }
            changeGisColor(this.dialogGIS);

            // 初始化地图数据
            this.initMapData();

            // 如果已有选中的道路数据，绘制道路
            if (this.selectedRoadData) {
                this.drawRoad(this.selectedRoadData);
            }
        },
        initMapData() {
            // 在地图上显示路况数据
            // 这里可以根据实际需求添加路况可视化逻辑
        },
        drawRoad(roadData) {
            if (!roadData || !roadData.roadSegments || roadData.roadSegments.length === 0) {
                return;
            }

            // 清除之前的道路图层
            this.clearRoadLayer();
            this.dialogGIS.layerList.divLayer.removeAll();

            // 准备数据
            let htStep = 0;
            const roadColor = this.getRoadColor(roadData.roadCondition);
            let datas = roadData.roadSegments.map((item) => {
                return {
                    points: [
                        {
                            lat: Number(item.startLatLng.split(',')[0]),
                            lng: Number(item.startLatLng.split(',')[1]),
                            ht: htStep * 0
                        },
                        {
                            lat: Number(item.endLatLng.split(',')[0]),
                            lng: Number(item.endLatLng.split(',')[1]),
                            ht: htStep * 0
                        }
                    ],
                    color: roadColor
                };
            });

            // 创建图层
            this.roadLayer = new this.dialogGIS.layer();
            this.roadLayer.visible = true;
            this.dialogGIS.gis.scene.add(this.roadLayer.Group);

            // 开启保持道路像素大小
            datas.autoScale = true;
            // 道路宽度
            datas.width = 3;
            // 创建道路模型
            let roadMesh = this.dialogGIS.meshList.road.create(datas);
            // 模型添加进图层
            this.roadLayer.add(roadMesh);
            // 更新GIS
            this.dialogGIS.gis.needUpdate = true;
            // // 绘制起点终点
            // let roadEndsData = roadEnds.map((item) => ({
            //     dom: `
            //     <style>
            //     .road-point {
            //         width: 150px;
            //         height: 150px;
            //         border: 10px solid ${'#ff0000' || roadColor}; /* 蓝色外圆环 */
            //         border-radius: 50%;
            //         background:
            //             radial-gradient(
            //                 circle at center,
            //                 ${'#ff0000' || roadColor} 0%,     /* 红色中心实心圆 */
            //                 ${'#ff0000' || roadColor} 33%,    /* 实心圆占据1/3半径 */
            //                 transparent 33%, /* 透明区域 */
            //                 transparent 100%
            //             );
            //         box-sizing: border-box;
            //         transition: all 0.3s ease;
            //     }
            //     </style>
            //     <div class="road-point"></div>
            //     `,
            //     point: {
            //         lng: item.split(',')[1],
            //         lat: item.split(',')[0]
            //     }
            // }));
            // roadEndsData.forEach((d) => {
            //     this.dialogGIS.layerList.divLayer.addDiv(d);
            // });

            // 缩放到道路区域
            const regionCoorsList = roadData.roadSegments
                .map((item) => {
                    return {
                        lat: Number(item.startLatLng.split(',')[0]),
                        lng: Number(item.startLatLng.split(',')[1])
                    };
                })
                .concat(
                    roadData.roadSegments.map((item) => {
                        return {
                            lat: Number(item.endLatLng.split(',')[0]),
                            lng: Number(item.endLatLng.split(',')[1])
                        };
                    })
                );
            const msgPoint = this.getBoundingBoxCenterOptimized(regionCoorsList);
            // this.drawMsg(msgPoint, roadData);

            // 点击事件
            this.dialogGIS.event.addClick(
                this.roadLayer,
                (thData, event) => {
                    this.drawMsg(msgPoint, roadData);
                },
                () => {
                    this.msgDivLayer && this.msgDivLayer.remove();
                }
            );

            this.dialogGIS.cameraControl.zoomByPoints(regionCoorsList, 1.4);
        },
        drawMsg(msgPoint, msgData) {
            if (!msgPoint) {
                return;
            }
            this.msgDivLayer && this.msgDivLayer.remove();
            let data = {
                dom: `
            <style>
            .msg-data-window {
                position: relative;
                width: 196px;
                height: 156px;
                transform: translate(-50%, -100%);
                background-image: url(${msgBg});
                background-size: 100% 100%;
                background-repeat: no-repeat;
                z-index: 1000;
            }
            .msg-data-window-title {
                position: absolute;
                top: 23px;
                left: 42px;
                background-size: 100% 100%;
                background-repeat: no-repeat;
                display: flex;
                justify-content: space-between;
            }
            .msg-data-window-title span {
                display: block;
                font-family: PangMenZhengDao-3, PangMenZhengDao-3, PingFang SC;
                font-weight: 400;
                font-size: 15px;
                color: #ffffff;
            }
            .msg-data-window-main {
                position: absolute;
                top: 48px;
                left: 42px;
                right: 42px;
                bottom: 56px;
                display: flex;
                align-items: center;
            }
            .msg-data-window-main-name {
                font-family: Source Han Sans CN, Source Han Sans CN, PingFang SC;
                font-weight: bold;
                font-size: 11px;
                color: #a1aeba;
            }
            .msg-data-window-main-value {
                font-family: Source Han Sans CN, Source Han Sans CN, PingFang SC;
                font-weight: bold;
                font-size: 11px;
                color: #ffffff;
                min-width: 0;
                flex: 1;
                overflow: hidden;
                display: -webkit-box;
                line-clamp: 1;
                -webkit-line-clamp: 1;
                -webkit-box-orient: vertical;
                text-overflow: ellipsis;
            }
        </style>
        <div class="msg-data-window">
            <div class="msg-data-window-title">
                <span>人流量</span>
            </div>
            <main class="msg-data-window-main">
                <span class="msg-data-window-main-name">总人数：</span>
                <span class="msg-data-window-main-value" title="${msgData.personFlow}"
                    >${msgData.personFlow}</span
                >
            </main>
        </div>`,
                point: msgPoint
            };
            this.msgDivLayer = this.dialogGIS.layerList.divLayer.addDiv(data);
        },
        clearRoadLayer() {
            if (this.roadLayer) {
                this.dialogGIS.gis.scene.remove(this.roadLayer.Group);
                this.roadLayer = null;
                this.dialogGIS.gis.needUpdate = true;
            }
        },
        getRoadColor(condition) {
            const colorMap = {
                畅通: 0x25d725,
                基本畅通: 0xc4ff4e,
                拥堵: 0xd7a225,
                严重拥堵: 0xee3e23
            };
            return colorMap[condition] || 0x25d725;
        },
        selectRoadData(roadData) {
            this.selectedRoadData = roadData;
            if (this.dialogGIS) {
                this.drawRoad(roadData);
            }
            // 重置时间轴到第1小时并重新开始轮播
            this.currentHour = 1;
            if (this.$refs.timeline) {
                this.$refs.timeline.reset(1);
            }
        },
        onHourChange(hour) {
            // 根据选中的小时更新人流量数据显示
            this.updateTrafficData(hour);
        },
        updateTrafficData(hour) {
            // 模拟根据小时更新人流量数据
            if (this.selectedRoadData) {
                // 这里可以根据实际需求更新地图上的人流量显示
                console.log(`更新第${hour}小时的人流量数据`);
            }
        },
        refreshTable() {
            this.$refs.listTable && this.search();
        },
        onRowClick(row) {
            // 用户点击表格行时重新绘制选中道路
            this.selectRoadData(row);
        },
        // 获取经纬度列表中心
        getBoundingBoxCenterOptimized(coordinates) {
            if (!coordinates || coordinates.length === 0) {
                return null;
            }

            if (coordinates.length === 1) {
                return {
                    lat: coordinates[0].lat,
                    lng: coordinates[0].lng
                };
            }

            // 使用解构赋值初始化
            let { lat: minLat, lng: minLng } = coordinates[0];
            let { lat: maxLat, lng: maxLng } = coordinates[0];

            // 从第二个元素开始遍历
            for (let i = 1; i < coordinates.length; i++) {
                const { lat, lng } = coordinates[i];

                if (lat < minLat) minLat = lat;
                else if (lat > maxLat) maxLat = lat;

                if (lng < minLng) minLng = lng;
                else if (lng > maxLng) maxLng = lng;
            }

            return {
                lat: (minLat + maxLat) / 2,
                lng: (minLng + maxLng) / 2
            };
        }
    },
    mounted() {
        this.refreshTable();
        // 延迟启动时间轴自动播放，确保数据加载完成
        this.$nextTick(() => {
            if (this.$refs.timeline) {
                this.$refs.timeline.start();
            }
        });
    },
    beforeDestroy() {
        // 清理资源
        this.clearRoadLayer();
    }
};
</script>
<style lang="less" scoped>
.table-gis-content {
    box-shadow: 0 2px 10px 0 rgba(0, 0, 0, 0.05);
    background: rgba(0, 42, 92, 0.4);
    border-radius: 4px;
    height: 100%;
    display: flex;
    flex-direction: column;
    padding: 18px 20px;

    .search-container {
        height: 44px;
        margin-bottom: 16px;

        .tools {
            width: 100%;
            display: flex;
            justify-content: space-between;
        }
    }

    .content-grid {
        flex: 1;
        height: 0;
        display: flex;
        gap: 16px;

        .table-section {
            min-width: 0;
            flex: 4;
            display: flex;
            flex-direction: column;
            border-radius: 4px;

            .list-table {
                flex: 1;
                height: 0;
            }
        }

        .gis-section {
            min-width: 0;
            flex: 6;
            display: flex;
            flex-direction: column;
            border-radius: 4px;
            position: relative;

            .road-condition-map {
                flex: 1;
                height: 100%;
                border-radius: 4px;
                overflow: hidden;
            }

            .road-condition-legend {
                position: absolute;
                right: 72px;
                bottom: 90px;
                width: 132px;
                height: fit-content;
                border: 1px solid #47c0ffff;
                background-color: #013b70bd;
                border-radius: 7px;
                padding: 12px 24px;
                display: flex;
                flex-direction: column;
                p {
                    font-family:
                        Source Han Sans CN,
                        Source Han Sans CN;
                    font-weight: 500;
                    font-size: 11px;
                    color: #ffffff;
                    margin-bottom: 6px;
                }
                .legend-content {
                    min-height: 0;
                    flex: 1;
                    display: flex;
                    flex-direction: column;
                    gap: 4px;
                    .legend-item {
                        display: flex;
                        align-items: center;
                        gap: 8px;
                    }
                    .legend-color {
                        display: block;
                        --color: #25d725;
                        width: 12px;
                        height: 0px;
                        border: 2px solid var(--color);
                    }
                    .legend-name {
                        font-family:
                            Source Han Sans CN,
                            Source Han Sans CN;
                        font-weight: 400;
                        font-size: 12px;
                        color: #c9dfff;
                    }
                }
            }
        }
    }
}

.p-0 {
    padding: 0;
}

/deep/ .el-button {
    &--text {
        color: #0095ff;
        &:hover {
            color: #fff;
        }
    }
}
</style>
