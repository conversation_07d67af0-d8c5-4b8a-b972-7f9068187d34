<template>
    <div class="header">
        <div class="title">
            <span class="text">山东位置能力 ·</span>
            <span>城市路网</span>
        </div>
        <div class="nav">
            <el-button class="nav-btn" type="primary" @click="goBack" v-if="nav.showBack">
                <template #default>
                    <img
                        src="../../../../../img/common/back-icon.png"
                        alt="返回"
                        class="back-icon"
                    />
                    返回
                </template>
            </el-button>
            <span class="nav-text-before">{{ nav.beforePathName }}&nbsp;</span>
            <span class="nav-text-last">{{ nav.lastPathName }}</span>
        </div>
    </div>
</template>

<script>
export default {
    name: 'Header',
    props: {
        nav: {
            type: Object,
            default: () => ({
                showBack: true,
                beforePathName: '',
                lastPathName: ''
            })
        }
    },
    data() {
        return {};
    },
    methods: {
        goBack() {
            this.$emit('goBack');
        }
    }
};
</script>

<style lang="less" scoped>
.header {
    margin-bottom: 16px;
    .title {
        font-size: 36px;
        font-weight: bold;
        color: #fff;
        margin-bottom: 12px;
        .text {
            color: #135ef4;
        }
    }

    .nav {
        box-shadow: 0 2px 10px 0 rgba(0, 0, 0, 0.05);
        background: rgba(0, 50, 102, 0.4);
        border-radius: 4px;
        height: 54px;
        display: flex;
        align-items: center;
        padding-left: 16px;

        .back-icon {
            width: 16px;
            height: 16px;
            margin-right: 0px;
        }

        .nav-btn {
            width: 84px;
            height: 32px;
            line-height: 32px;
            font-size: 14px;
            font-weight: bold;
            color: #fff;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-right: 16px;
        }
        /deep/.el-button {
            span {
                display: flex;
                gap: 6px;
                align-items: center;
                justify-content: center;
            }
        }

        .nav-text-before {
            font-size: 16px;
            font-weight: bold;
            color: rgba(255, 255, 255, 0.45);
        }

        .nav-text-last {
            font-size: 16px;
            font-weight: bold;
            color: rgba(255, 255, 255, 0.85);
        }
    }
}
</style>
