<template>
    <el-input
        v-if="['number', 'Number'].includes($attrs.type)"
        :style="[$attrs.itemStyle || {}]"
        ref="input"
        v-model.number="currentVal"
        @input="changeInput"
        :class="addClass"
        v-bind="$attrs"
        v-on="$listeners"
        class="form-input"
    >
        <template v-for="slotName of slotNames" #[slotName]>
            <slot :name="`${$attrs.enName}${slotName}`"></slot>
        </template>
    </el-input>
    <el-input
        v-else
        :style="[$attrs.itemStyle || {}]"
        ref="input"
        v-model="currentVal"
        @input="changeInput"
        :class="addClass"
        v-bind="$attrs"
        v-on="$listeners"
        class="form-input"
    >
        <template v-for="slotName of slotNames" #[slotName]>
            <slot :name="`${$attrs.enName}${slotName}`"></slot>
        </template>
    </el-input>
</template>

<script>
export default {
    name: 'RamsInput',
    props: {
        value: {
            type: [Number, String]
        },
        slotNames: {
            type: Array
        },
        addClass: {
            type: [Object, Array, String]
        },
        defaultValue: {
            type: [Number, String],
            default: ''
        }
    },
    data() {
        return {
            currentVal: this.defaultValue || this.value
        };
    },

    watch: {
        value(val) {
            this.currentVal = val;
        }
    },

    methods: {
        changeInput(newVal) {
            this.$emit('input', newVal);
        },
        focus() {
            this.$refs.input.focus();
        }
    }
};
</script>
<style lang="less" scoped>
.form-input {
    /deep/.el-input__inner {
        background: #013b70;
        border: 1px solid #47c0ff;
        color: #91aac1;
        font-weight: 500;
    }
}
</style>
