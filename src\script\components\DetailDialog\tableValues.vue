<template>
    <div class="content-container">
        <div class="table-container">
            <!-- 表头 -->
            <div class="table-header" :style="{ gridTemplateColumns }">
                <div class="header-item" v-for="(item, index) in headers" :key="index">
                    {{ item }}
                </div>
            </div>
            <!-- 表格内容 -->
            <template>
                <div class="table-body custom-scrollbar" v-if="tableData.length">
                    <div
                        class="table-row"
                        :style="{ gridTemplateColumns }"
                        v-for="(item, index) in tableData"
                        :key="index"
                    >
                        <div
                            class="table-cell"
                            v-for="(value, key) in item"
                            :key="'index' + key"
                            :title="value"
                        >
                            {{ value }}
                        </div>
                    </div>
                </div>
                <el-empty description="暂无数据" v-else></el-empty>
            </template>
        </div>
    </div>
</template>

<script>
export default {
    props: {
        resultData: {
            type: Array,
            default: () => []
        }
    },
    watch: {
        resultData: {
            handler(newVal) {
                this.getTableData(newVal);
            },
            deep: true
        }
    },
    data() {
        return {
            headers: [],
            tableData: []
        };
    },
    computed: {
        gridTemplateColumns() {
            return this.headers.map(() => '1fr').join(' ');
        }
    },
    methods: {
        getTableData(data) {
            if (data.length) {
                this.headers = Object.keys(data[0]);
                this.tableData = data;
            } else {
                this.tableData = [];
            }
        }
    }
};
</script>

<style lang="less" scoped>
.content-container {
    width: 100%;
    height: 55vh;
    .table-container {
        width: 100%;
        height: 100%;
        background: #002a59;
        padding: 1.11rem;
        display: flex;
        flex-direction: column;

        .table-header {
            display: grid;
            // grid-template-columns: 1.5fr 1fr 1fr 1fr 1.2fr 1fr;
            background: #083e79;
            border-radius: 0.94rem;

            .header-item {
                height: 1.89rem;
                line-height: 1.89rem;
                text-align: center;
                color: #c3dff6;
                font-size: 0.83rem;
                font-weight: normal;
                border-left: 2px solid rgba(255, 255, 255, 0.17);
            }
            .header-item:first-child {
                border-left: none;
            }
        }

        .table-body {
            min-height: 0;
            flex: 1;
            overflow-y: auto;
            .table-row {
                height: 1.89rem;
                line-height: 1.89rem;
                display: grid;
                // grid-template-columns: 1.5fr 1fr 1fr 1fr 1.2fr 1fr;
                align-items: center;
                transition: all 0.3s;
                border-radius: 0.94rem; // 每行的圆角

                &:nth-child(even) {
                    background: #085195;
                }
                &:nth-child(odd) {
                    background: #002a59;
                }

                .table-cell {
                    text-align: center;
                    font-size: 14px;
                    color: #c3dff6;
                    white-space: nowrap;
                    overflow: hidden;
                    text-overflow: ellipsis;
                    padding: 0 10px;
                    border-left: 2px solid rgba(255, 255, 255, 0.17);
                }
                .table-cell:first-child {
                    border-left: none;
                }
            }
        }
    }
}
</style>
