<template>
    <div class="card">
        <div class="title">
            <span>{{ title }}</span>
        </div>
        <slot></slot>
    </div>
</template>

<script>
export default {
    name: 'card-component',
    props: {
        title: {
            type: String,
            required: true
        }
    }
};
</script>

<style lang="less" scoped>
.card {
    height: 100%;
    flex: 1;
    background: rgba(0, 42, 92, 0.4);
    border-radius: 4px;
    margin-bottom: 10px;

    .title {
        padding: 12px 32px;
        border-bottom: 1px solid rgba(64, 155, 255, 0.4);
        flex-shrink: 0;

        span {
            position: relative;
            font-family: SourceHanSansCN, SourceHanSansCN;
            font-weight: bold;
            font-size: 16px;
            color: rgba(255, 255, 255, 0.85);
            line-height: 24px;
            padding-left: 12px;

            &::before {
                content: '';
                position: absolute;
                width: 2px;
                top: 3px;
                left: 0;
                height: 16px;
                background: #0091ff;
                border-radius: 1px;
            }
        }
    }
}
</style>
