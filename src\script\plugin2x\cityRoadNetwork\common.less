/deep/.el-button {
    border: none !important;
    border-radius: 4px !important;
    font-size: 14px !important;
    &--primary {
        background: #0095ff !important;
    }
}
/deep/.el-form-item__label {
    color: rgba(255, 255, 255, 0.65);
}
/deep/.el-range-input {
    background-color: transparent !important;
    color: #fff !important;
    &::placeholder {
        color: rgba(255, 255, 255, 0.45);
    }
}
/deep/ .el-input,
/deep/ .el-select {
    &.is-disabled {
        .el-input__inner {
            background: #061c3d !important;
            border: 1px solid rgba(18, 139, 207, 0.4) !important;
        }
    }
}
/deep/.el-input--suffix .el-input__inner {
    padding-right: 30px !important;
}
/deep/ .el-input__inner,
/deep/ .el-textarea__inner {
    font-family: SourceHanSansCN, SourceHanSansCN;
    font-weight: bold;
    font-size: 14px;
    color: #fff;
    background: rgba(4, 52, 106, 0.6);
    padding: 0 10px;
    border-radius: 2px;
    border: 1px solid rgba(18, 139, 207, 0.6);

    &::placeholder {
        color: rgba(255, 255, 255, 0.45);
    }
    .el-range-separator {
        color: #fff !important;
    }

    &:focus {
        background: rgba(4, 52, 106, 0.6);
        border-radius: 2px;
        border: 1px solid rgba(18, 139, 207, 0.6);
    }

    &:hover {
        background: rgba(4, 52, 106, 0.6);
        border-radius: 2px;
        border: 1px solid rgba(18, 139, 207, 0.6) !important;
    }

    // 自定义滚动条样式
    &::-webkit-scrollbar {
        width: 6px !important; // 设置滚动条宽度为10px
    }
    &::-webkit-scrollbar-thumb {
        border-radius: 10px;
        box-shadow: inset 0 0 5px rgba(0, 0, 0, 0.1);
        background: #0b66b3;
    }
    &::-webkit-scrollbar-track {
        /* 滚动条里面轨道 */
        box-shadow: inset 0 0 5px rgba(0, 0, 0, 0.1);
        border-radius: 10px;
        background: transparent;
    }
    &::-webkit-scrollbar-corner {
        background: rgba(0, 0, 0, 0);
    }
}
/deep/.el-tabs {
    &__item {
        color: #fff !important;
        &.is-active {
            color: #0095ff !important;
        }
    }
    &__nav-wrap::after {
        display: none !important;
    }
}
/deep/.date-editor-padding {
    width: 100% !important;
    .el-input__inner {
        padding-left: 30px !important;
    }
}
.page-container {
    background-image: url('../../../img/common/manege-bg.png') !important;
    background-size: 100% 100% !important;
    background-repeat: no-repeat !important;
}
