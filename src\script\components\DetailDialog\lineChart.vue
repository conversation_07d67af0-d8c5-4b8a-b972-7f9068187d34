<template>
    <div class="content-container">
        <div ref="lineChart" class="line-chart"></div>
        <div class="no-data" v-if="resultData && resultData.length == 0">暂无数据</div>
    </div>
</template>

<script>
import * as echarts from 'echarts';

export default {
    props: {
        resultData: {
            type: Array,
            default: () => []
        }
    },
    watch: {
        resultData: {
            handler(newVal) {
                console.log(newVal);

                this.getChartData(newVal);
            },
            deep: true
        }
    },
    data() {
        return {
            chart: null,
            chartData: {
                xData: [],
                yData: {}
            }
        };
    },
    methods: {
        // 生成随机颜色
        getRandomColor() {
            // 预定义一些好看的颜色
            const colors = [
                '#4B8BF4', // 蓝色
                '#FF6E76', // 红色
                '#19D1A3', // 绿色
                '#9287E7', // 紫色
                '#FFB72C', // 黄色
                '#5470C6', // 深蓝
                '#91CC75', // 浅绿
                '#FAC858', // 橙色
                '#EE6666', // 粉红
                '#73C0DE' // 天蓝
            ];
            return colors;
        },
        resetChartData() {
            this.chartData = {
                xData: [],
                yData: {}
            };
        },
        getChartData(data) {
            this.resetChartData();
            // 如果数据为空，清空图表
            if (!data || data.length === 0) {
                if (this.chart) {
                    this.chart.clear();
                }
                return;
            }
            let charObj = {};
            let time = [];
            let chartData = this.dealChartData(data);
            chartData.forEach((item) => {
                time.push(item.time);
                if (!charObj.hasOwnProperty(item.type)) {
                    charObj[item.type] = [];
                }
                charObj[item.type].push(item.value);
            });
            this.chartData.xData = [...new Set(time)];
            this.chartData.yData = charObj;
            this.initChart();
        },

        initChart() {
            this.chart = echarts.init(this.$refs.lineChart);
            let seriesData = [];
            const colorMap = {}; // 存储每个系列的颜色
            let i = 0;
            for (let k in this.chartData.yData) {
                // 获取随机颜色
                const color = this.getRandomColor()[i];
                colorMap[k] = color; // 保存每个系列的颜色
                seriesData.push({
                    name: k,
                    data: this.chartData.yData[k],
                    type: 'line',
                    showSymbol: false,
                    lineStyle: {
                        color: color, // 使用随机颜色
                        width: 2
                    },
                    emphasis: {
                        scale: true,
                        focus: 'series',
                        itemStyle: {
                            color: color // 使用相同的随机颜色
                        }
                    },
                    areaStyle: {
                        color: {
                            type: 'linear',
                            x: 0,
                            y: 0,
                            x2: 0,
                            y2: 1,
                            colorStops: [
                                {
                                    offset: 0,
                                    color: `${color}6A` // 添加透明度
                                },
                                {
                                    offset: 1,
                                    color: `${color}0D` // 更浅的透明度
                                }
                            ]
                        }
                    }
                });
                i++;
            }
            const option = {
                grid: {
                    top: '10%',
                    left: '3%',
                    right: '3%',
                    bottom: '5%',
                    containLabel: true
                },

                tooltip: {
                    trigger: 'axis',
                    backgroundColor: '#013B70', // 设置为半透明黑色
                    borderColor: '#47C0FF', // 边框颜色
                    borderWidth: 1, // 边框宽度
                    textStyle: {
                        color: '#fff', // 文字颜色
                        fontSize: 14
                    },
                    axisPointer: {
                        type: 'line',
                        lineStyle: {
                            color: '#4B8BF4',
                            type: 'dashed'
                        }
                    },
                    formatter: (params) => {
                        let str = `<div style="margin-bottom: 10px;background: #013B70;font-family: Source Han Sans CN, Source Han Sans CN;">${params[0].name}</div>`;
                        params.forEach((item) => {
                            // 使用对应系列的颜色
                            str += `
                        <div>
                            <span style="display: inline-block;">
                                ${item.seriesName}：
                            </span>
                            <span style="color: #13F3FB; font-weight: 500;font-family: Source Han Sans CN, Source Han Sans CN;">${item.value}</span>
                        </div>`;
                        });
                        return `<div>${str}</div>`;
                    }
                },
                xAxis: {
                    type: 'category',
                    data: this.chartData.xData,
                    axisLine: {
                        show: true,
                        lineStyle: {
                            color: '#77AAEA'
                        }
                    },
                    axisTick: {
                        show: false
                    },
                    axisLabel: {
                        color: '#92B9E5'
                    },
                    splitLine: {
                        show: true,
                        lineStyle: {
                            color: '#77AAEA',
                            type: 'dashed'
                        }
                    }
                },
                yAxis: {
                    type: 'value',
                    name: '人次',
                    nameTextStyle: {
                        color: '#92B9E5'
                    },
                    axisLine: {
                        show: false
                    },
                    axisTick: {
                        show: false
                    },
                    axisLabel: {
                        color: '#92B9E5'
                    },
                    splitLine: {
                        lineStyle: {
                            color: '#77AAEA',
                            type: 'dashed'
                        }
                    }
                },
                series: seriesData
            };
            this.chart.setOption(option);
        },
        dealChartData(data) {
            let chartData = data.reduce((result, item) => {
                // 添加流入人数数据
                result.push({
                    time: item.key,
                    type: '人数', //暂时写死
                    value: item.value
                });
                return result;
            }, []);

            return chartData;
        }
    }
};
</script>

<style lang="less" scoped>
.content-container {
    width: 100%;
    height: 55vh;
    position: relative;
    .line-chart {
        width: 100%;
        height: 100%;
    }
    .no-data {
        width: 100%;
        position: absolute;
        text-align: center;
        top: 2.78rem;
    }
}
</style>
