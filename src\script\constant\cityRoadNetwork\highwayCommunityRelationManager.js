import { SHANDONG_CITY } from '@/script/constant/shandong';
const communityFormCols = [
    {
        prop: 'cityName',
        label: '地市名称',
        element: 'el-select',
        attrs: {
            placeholder: '请选择',
            clearable: true,
            'popper-class': 'gdb-select-dropdown-dark gdb-popover-dark'
        },
        slot: {
            element: 'el-option',
            enums: SHANDONG_CITY({ isCode: false })
        },
        span: 4
    },
    {
        prop: 'highwayName',
        label: '高速道路名称',
        labelWidth: '180',
        element: 'el-input',
        attrs: {
            placeholder: '请输入',
            clearable: true
        },
        span: 4
    },
    {
        span: 16
    }
];

const communityTableCols = [
    {
        prop: 'cityName',
        label: '地市名称'
    },
    {
        prop: 'roadName',
        label: '高速道路名称'
    },
    {
        prop: 'eciNum',
        label: '小区ECI数量'
    },
    {
        prop: 'eciValid',
        label: '小区有效性'
    },
    {
        prop: 'operation',
        label: '操作',
        width: 180
    }
];

const communityCheckTableCols = [
    {
        prop: 'indexNo',
        label: '序号',
        width: '50'
    },
    {
        prop: 'cityName',
        label: '所属区域'
    },
    {
        prop: 'eciId',
        label: '小区ID'
    },
    {
        prop: 'eciName',
        label: '小区名称'
    },
    {
        prop: 'lastUpdateTime',
        label: '最后更新时间',
        'min-width': 180
    }
];

export { communityFormCols, communityTableCols, communityCheckTableCols };
