<template>
    <commonTableContent
        ref="stationInfoListTable"
        :fields="fields"
        :initialForm="initialForm"
        addButtonText="新增高铁站点"
        :columns="columns"
        :data="tableData"
        :dataTotal="total"
        @get-table-data="handleGetTableData"
        @action="handleAction"
        @delete="handleDelete"
    >
        <template #city="{ row }">
            <span>{{ getLabelByValue(SHANDONG_CITY, row.city) }}</span>
        </template>
        <template #district="{ row }">
            <span>{{ getLabelByValue(SHANDONG_DISTRICT(row.city), row.district) }}</span>
        </template>
        <template #highSpeed="{ row }">
            <span>{{ row.highSpeed ? '是' : '否' }}</span>
        </template>
        <template #interCity="{ row }">
            <span>{{ row.interCity ? '是' : '否' }}</span>
        </template>
        <template #moving="{ row }">
            <span>{{ row.moving ? '是' : '否' }}</span>
        </template>
        <template #regular="{ row }">
            <span>{{ row.regular ? '是' : '否' }}</span>
        </template>
        <template #coorsType="{ row }">
            <span>{{ getLabelByValue(COORS_TYPE, row.coorsType) }}</span>
        </template>
    </commonTableContent>
</template>

<script>
import commonTableContent from '../../../components/commonTableContent/index.vue';
import {
    stationFormCols,
    stationTableCols,
    COORS_TYPE
} from '@/script/constant/cityRoadNetwork/stationInfoManager.js';
import { getLabelByValue } from '@/script/utils/method.js';
import { SHANDONG_CITY, SHANDONG_DISTRICT } from '@/script/constant/shandong';

export default {
    name: 'table-content',
    components: {
        commonTableContent
    },
    data() {
        return {
            initialForm: {
                city: '',
                regionName: ''
            },
            total: 0,
            tableData: [],
            getLabelByValue
        };
    },
    computed: {
        fields() {
            return stationFormCols;
        },
        columns() {
            return stationTableCols;
        },
        SHANDONG_CITY() {
            return SHANDONG_CITY();
        },
        SHANDONG_DISTRICT() {
            return (cityId) => SHANDONG_DISTRICT({ cityId });
        },
        COORS_TYPE() {
            return COORS_TYPE;
        }
    },
    methods: {
        async handleGetTableData(params, pagination) {
            const payload = {
                city: params.city,
                regionName: params.regionName,
                pageNo: pagination.curPage,
                pageSize: pagination.pageSize
            };

            let { data } = await $request(
                'post',
                'mtexapi/region-service/management/train/station/query',
                payload
            );
            this.tableData = data.data;
            this.total = data.total;
        },
        handleAction(action, row = {}) {
            // 将action转发到父组件
            this.$emit('action', action, row);
        },
        handleDelete(row) {
            this.$confirm('确定要删除吗？', '提示', {
                confirmButtonText: '确定',
                cancelButtonText: '取消',
                type: 'warning'
            }).then(() => {
                $request('post', 'mtexapi/region-service/management/train/station/delete', {
                    ids: [row.id]
                })
                    .then(({ serviceFlag, returnMsg }) => {
                        if (serviceFlag === 'TRUE') {
                            this.refreshTable();
                            this.$message({
                                type: 'success',
                                message: '删除成功'
                            });
                        } else {
                            this.$message({
                                type: 'error',
                                message: returnMsg
                            });
                        }
                    })
                    .catch(() => {});
            });
        },
        refreshTable() {
            this.$refs.stationInfoListTable.search();
        }
    },
    mounted() {
        this.refreshTable();
    }
};
</script>
