<template>
    <div class="check-content">
        <detailContent :row="row" action="check" />
        <Card title="站点可视化展示">
            <section class="section-wrapper">
                <div class="section-container">
                    <div class="gdb-section-title">站点区域地图预览</div>
                    <div class="gis-content">
                        <mtv-gis
                            class="home-map"
                            ref="mtvGis"
                            :totaloptions="gistotalOptions"
                            @onLoad="gisOnLoad"
                            :autoActive="false"
                        ></mtv-gis>
                    </div>
                </div>
            </section>
        </Card>
    </div>
</template>

<script>
import Card from '../../../components/Card/index.vue';
import detailContent from '../detailContent/index.vue';
import { gistotalOptions, getMayType, changeGisColor } from '@/script/constant/gis.js';

export default {
    name: 'check-content',
    components: {
        Card,
        detailContent
    },
    props: {
        row: {
            type: Object,
            default: () => ({})
        }
    },
    data() {
        return {
            gistotalOptions
        };
    },
    methods: {
        gisOnLoad() {
            this.dialogGIS = this.$refs.mtvGis.getEntity();
            // 设置底图
            if (getMayType() === 'default') {
                this.dialogGIS.tileLayerList['底图图层'].visible = false;
                //重写getUrl方法
                this.dialogGIS.tileLayerList['高德底图'].getUrl = (x, y, z) => {
                    return `${location.origin}/MapUrl/api/appmaptile?x=${x}&y=${y}&z=${z}`;
                };
            } else {
                this.dialogGIS.tileLayerList['高德底图'] &&
                    (this.dialogGIS.tileLayerList['高德底图'].visible = false);
            }
            changeGisColor(this.dialogGIS);

            this.drawStationOutline();
        },
        drawStationOutline() {
            let data = [];
            if (this.row.regionCoors && this.row.regionCoors.startsWith('POLYGON')) {
                data = [this.row.regionCoors.split('((')[1].split('))')[0]];
            } else {
                data = [this.row.regionCoors];
            }
            let ans = [];
            for (let i = 0; i < data.length; i++) {
                let string = data[i];
                let latlngs = string.match(/[^;]+/g);
                let points = [];
                for (let e = 0; e < latlngs.length; e++) {
                    let items = latlngs[e];
                    items = items.match(/[^,]+/g);
                    let lat = Number(items[1]),
                        lng = Number(items[0]);
                    points.push({ lat: lat, lng: lng });
                }
                ans.push({ points: points, color: 0x0095ff });
            }
            ans.width = 3;
            ans.autoScale = true;
            let mesh = this.dialogGIS.meshList.road.create(ans);
            mesh.material.depthTest = false;
            mesh.material.transparent = true;
            this.dialogGIS.gis.scene.add(mesh);

            this.dialogGIS.gis.needUpdate = true;
            const regionCoorsList = data[0].split(';').map((item) => {
                return {
                    lat: Number(item.split(',')[1]),
                    lng: Number(item.split(',')[0])
                };
            });
            this.dialogGIS.cameraControl.zoomByPoints(regionCoorsList, 1.2);
        }
    },
    mounted() {}
};
</script>

<style lang="less" scoped>
.check-content {
    min-height: 100%;
    height: max-content;
    display: flex;
    flex-direction: column;
    gap: 6px;

    .section-wrapper {
        display: flex;
        justify-content: center;
        align-items: center;
        overflow-y: auto;
        overflow-x: hidden;

        .section-container {
            width: 60%;
            height: 100%;

            .gis-content {
                position: relative;
                width: 100%;
                height: 0;
                padding: 0;
                padding-bottom: 56.25%;
                margin-bottom: 24px;

                .home-map {
                    border-radius: 2px;
                    border: 1px solid rgba(64, 155, 255, 0.3);
                    position: absolute;
                    top: 0;
                    right: 0;
                    bottom: 0;
                    left: 0;
                }
            }
        }
    }
}
</style>
